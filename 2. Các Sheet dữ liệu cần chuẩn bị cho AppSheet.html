<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Các Sheet dữ liệu cần chuẩn bị cho AppSheet</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
        }
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #e0e0e0;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="p-4 md:p-8">
    <div class="max-w-3xl mx-auto bg-white shadow-xl rounded-lg p-6 md:p-8 border border-gray-200">
        <h1 class="text-3xl md:text-4xl font-bold text-indigo-800 mb-6 text-center">
            Các Sheet dữ liệu cần chuẩn bị để triển khai AppSheet
        </h1>
        <p class="text-gray-700 leading-relaxed mb-6">
            Để triển khai ứng dụng AppSheet quản lý lịch thu hoạch và vận chuyển, bạn cần chuẩn bị một Google Sheet duy nhất chứa các tab (sheets) sau đây. Mỗi tab sẽ đóng vai trò là một bảng dữ liệu trong AppSheet.
        </p>

        <div class="space-y-8">
            <!-- Main Data Sheets -->
            <div class="p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
                <h2 class="text-2xl font-semibold text-indigo-700 mb-4">I. Các Sheet dữ liệu chính</h2>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-4">
                    <li>
                        <strong class="text-gray-800 text-lg">`Har_Plan_Input`</strong>
                        <p class="ml-5">
                            **Mục đích:** Lưu trữ lịch thu hoạch thô ban đầu từ Bộ phận Khuyến nông.
                            <br>**Các cột quan trọng:** `ID_Har_Plan` (Khóa chính), `Ngày thu hoạch`, `Tỉnh`, `Huyện`, `Địa điểm`, `Loại cây trồng`, `Số lượng dự kiến (Kg)`.
                        </p>
                    </li>
                    <li>
                        <strong class="text-gray-800 text-lg">`Planning_Schedule`</strong>
                        <p class="ml-5">
                            **Mục đích:** Là bảng trung tâm, nơi Bộ phận Kế hoạch phân chia theo Zone, phân công cho Đội xe/Nhà cung cấp, và Đội xe/Nhà cung cấp cập nhật thông tin xe. Đây cũng là nơi theo dõi trạng thái của từng lịch trình vận chuyển.
                            <br>**Các cột quan trọng:** `ID_Planning_Schedule` (Khóa chính), `ID_Har_Plan_Ref` (Tham chiếu đến `Har_Plan_Input`), `Ngày vận chuyển`, `Zone`, `Đội xe/Nhà cung cấp được giao`, `Biển số xe`, `Số lượng thực tế thu hoạch (Kg)`, `Trạng thái`.
                        </p>
                    </li>
                </ul>
            </div>

            <!-- Master Data Sheets -->
            <div class="p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
                <h2 class="text-2xl font-semibold text-indigo-700 mb-4">II. Các Sheet dữ liệu Master Data (Tham khảo)</h2>
                <p class="text-gray-700 leading-relaxed mb-4">
                    Các sheet này chứa các danh mục cố định, giúp chuẩn hóa dữ liệu và tạo lựa chọn cho người dùng thay vì nhập tự do, giảm thiểu sai sót.
                </p>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-4">
                    <li>
                        <strong class="text-gray-800 text-lg">`Master_Zones`</strong>
                        <p class="ml-5">
                            **Mục đích:** Danh sách các Zone (khu vực) để Bộ phận Kế hoạch phân chia lịch trình.
                            <br>**Các cột quan trọng:** `Zone_ID` (Khóa chính), `Tên Zone`.
                        </p>
                    </li>
                    <li>
                        <strong class="text-gray-800 text-lg">`Master_Suppliers`</strong>
                        <p class="ml-5">
                            **Mục đích:** Danh sách các Nhà cung cấp/Đội xe có thể được phân công.
                            <br>**Các cột quan trọng:** `Supplier_ID` (Khóa chính), `Tên Nhà cung cấp/Đội xe`, `Email_Contact` (dùng cho phân quyền và gửi thông báo).
                        </p>
                    </li>
                    <li>
                        <strong class="text-gray-800 text-lg">`Master_Vehicles`</strong>
                        <p class="ml-5">
                            **Mục đích:** Danh sách các biển số xe mà các Nhà cung cấp/Đội xe thường sử dụng.
                            <br>**Các cột quan trọng:** `Vehicle_ID` (Khóa chính), `Biển số xe`, `Thuộc_Supplier_ID` (tham chiếu đến `Master_Suppliers` để liên kết xe với nhà cung cấp).
                        </p>
                    </li>
                    <li>
                        <strong class="text-gray-800 text-lg">`Master_GroupLine`</strong>
                        <p class="ml-5">
                            **Mục đích:** Danh sách các Group Line để gửi lịch vận chuyển cuối cùng.
                            <br>**Các cột quan trọng:** `GroupLine_ID` (Khóa chính), `Tên Group Line`, `Email_Contact` (dùng cho gửi thông báo).
                        </p>
                    </li>
                </ul>
            </div>
        </div>

        <p class="text-sm text-gray-500 text-center mt-8">
            Việc chuẩn bị đầy đủ các sheet này là bước cơ bản và quan trọng nhất để xây dựng một ứng dụng AppSheet hiệu quả và có cấu trúc dữ liệu tốt.
        </p>
    </div>
</body>
</html>
