<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON><PERSON> li<PERSON>u hướng dẫn triển khai AppSheet nội bộ cho CP SEEDS VIETNAM Co.LTD</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* Light gray background */
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #e0e0e0;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* Style for code blocks within HTML content for clarity */
        pre {
            background-color: #e2e8f0; /* bg-gray-200 */
            padding: 1rem;
            border-radius: 0.5rem; /* rounded-lg */
            overflow-x: auto;
            font-family: 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
            font-size: 0.875rem; /* text-sm */
            line-height: 1.5;
            color: #334155; /* text-slate-700 */
            border: 1px solid #cbd5e1; /* border-slate-300 */
        }
        .code-title {
            font-weight: 600; /* semibold */
            color: #1e293b; /* slate-900 */
            margin-bottom: 0.5rem;
            display: block;
        }
        .step-card {
            @apply bg-white p-6 rounded-lg shadow-md border border-gray-200 mb-6;
        }
        .step-heading {
            @apply text-xl font-semibold text-indigo-700 mb-3;
        }
        .sub-step {
            @apply ml-4 mt-2;
        }
        .example-table {
            @apply w-full text-sm text-left text-gray-500;
        }
        .example-table th {
            @apply px-4 py-2 font-medium text-gray-900 bg-gray-50;
        }
        .example-table td {
            @apply px-4 py-2;
        }
    </style>
</head>
<body class="p-4 md:p-8">
    <!-- Main Container -->
    <div class="max-w-5xl mx-auto bg-white shadow-xl rounded-lg p-6 md:p-10 border border-gray-200">
        <!-- Document Title -->
        <h1 class="text-3xl md:text-4xl font-bold text-indigo-800 mb-6 text-center">
            TÀI LIỆU HƯỚNG DẪN TRIỂN KHAI APPSHEET NỘI BỘ CHO CP SEEDS VIETNAM Co.LTD
        </h1>
        <p class="text-gray-600 text-center mb-8 text-lg">
            (Hệ thống quản lý lịch thu hoạch và vận chuyển)
        </p>

        <!-- Introduction -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">1. Giới thiệu</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Tài liệu này cung cấp hướng dẫn chi tiết từng bước để triển khai và cấu hình ứng dụng quản lý lịch thu hoạch và vận chuyển bằng AppSheet cho C.P SEEDS VIETNAM Co.LTD. Tài liệu được thiết kế để người dùng có kiến thức cơ bản về Google Sheet và AppSheet có thể thực hiện theo.
            </p>
            <p class="text-gray-700 leading-relaxed">
                Hệ thống này nhằm mục đích tự động hóa quy trình nghiệp vụ từ việc tiếp nhận lịch thu hoạch, phân công Zone, quản lý thông tin xe, đến việc phê duyệt và gửi lịch vận chuyển cuối cùng.
            </p>
        </div>

        <!-- Section 2: Chuẩn bị Google Sheet -->
        <div class="p-6 bg-blue-50 rounded-lg shadow-sm border border-blue-200 mb-10">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">2. Chuẩn bị Google Sheet (Cơ sở dữ liệu)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Đây là bước nền tảng để thiết lập cơ sở dữ liệu cho ứng dụng.
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li class="step-card">
                    <h3 class="step-heading">Bước 2.1: Tạo một Google Sheet mới</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Truy cập <a href="https://docs.google.com/spreadsheets/" target="_blank" class="text-blue-600 hover:underline font-medium">Google Sheets</a>.</li>
                        <li>Tạo một bảng tính mới bằng cách click vào biểu tượng <strong class="font-medium text-gray-800">"+"</strong> (Blank spreadsheet).</li>
                        <li>Đổi tên bảng tính thành: <strong class="font-medium text-gray-800">`CP SEEDS Logistics App Data`</strong>.</li>
                    </ul>
                    <div class="mt-4">
                        <img src="https://placehold.co/600x300/a3e635/0f172a?text=Tạo+Google+Sheet+mới" alt="Tạo Google Sheet mới" class="w-full h-auto rounded-md shadow-sm">
                        <p class="text-sm text-gray-500 mt-2 text-center">
                            [Hình ảnh: Giao diện tạo Google Sheet mới]
                        </p>
                    </div>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 2.2: Tạo các Tab (Sheets) và tiêu đề cột</h3>
                    <p class="text-gray-700 leading-relaxed mb-3">
                        Tạo từng tab theo danh sách dưới đây và nhập chính xác các tiêu đề cột vào **Dòng 1** (Row 1) của mỗi tab.
                    </p>

                    <div class="mb-5">
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Tab: `Har_Plan_Input`</h4>
                        <p class="text-gray-700 mb-2">
                            Thêm một tab mới và đổi tên thành `Har_Plan_Input`.
                            Nhập các tiêu đề cột sau vào Dòng 1:
                        </p>
                        <pre class="whitespace-pre-wrap"><code>ID_Har_Plan | Ngày thu hoạch | Tỉnh | Huyện | Địa điểm | Loại cây trồng | Số lượng dự kiến (Kg) | Ghi chú Khuyến nông</code></pre>
                        <table class="example-table mt-3">
                            <thead>
                                <tr><th>ID_Har_Plan</th><th>Ngày thu hoạch</th><th>Tỉnh</th><th>Huyện</th><th>Địa điểm</th><th>...</th></tr>
                            </thead>
                            <tbody>
                                <tr><td></td><td></td><td></td><td></td><td></td><td>...</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mb-5">
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Tab: `Planning_Schedule`</h4>
                        <p class="text-gray-700 mb-2">
                            Thêm một tab mới và đổi tên thành `Planning_Schedule`.
                            Nhập các tiêu đề cột sau vào Dòng 1:
                        </p>
                        <pre class="whitespace-pre-wrap"><code>ID_Planning_Schedule | ID_Har_Plan_Ref | Ngày vận chuyển | Zone | Đội xe/Nhà cung cấp được giao | Số lượng dự kiến (Kg) | Biển số xe | Tên tài xế | Số lượng thực tế thu hoạch (Kg) | Ghi chú Đội xe | Trạng thái | Ghi chú Kế hoạch</code></pre>
                        <table class="example-table mt-3">
                            <thead>
                                <tr><th>ID_Planning_Schedule</th><th>ID_Har_Plan_Ref</th><th>Ngày vận chuyển</th><th>Zone</th><th>...</th></tr>
                            </thead>
                            <tbody>
                                <tr><td></td><td></td><td></td><td></td><td>...</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mb-5">
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Tab: `Master_Zones`</h4>
                        <p class="text-gray-700 mb-2">
                            Thêm một tab mới và đổi tên thành `Master_Zones`.
                            Nhập các tiêu đề cột sau vào Dòng 1 và thêm dữ liệu mẫu:
                        </p>
                        <pre><code>Zone_ID | Tên Zone | Mô tả</code></pre>
                        <table class="example-table mt-3">
                            <thead>
                                <tr><th>Zone_ID</th><th>Tên Zone</th><th>Mô tả</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>ZONE001</td><td>Miền Bắc</td><td>Các tỉnh phía Bắc</td></tr>
                                <tr><td>ZONE002</td><td>Miền Trung</td><td>Các tỉnh miền Trung</td></tr>
                                <tr><td>ZONE003</td><td>Miền Nam</td><td>Các tỉnh phía Nam</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mb-5">
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Tab: `Master_Suppliers`</h4>
                        <p class="text-gray-700 mb-2">
                            Thêm một tab mới và đổi tên thành `Master_Suppliers`.
                            Nhập các tiêu đề cột sau vào Dòng 1 và thêm dữ liệu mẫu (quan trọng: cột `Email_Contact` phải là email thật để dùng cho phân quyền và thông báo):
                        </p>
                        <pre><code>Supplier_ID | Tên Nhà cung cấp/Đội xe | Email_Contact | Số điện thoại</code></pre>
                        <table class="example-table mt-3">
                            <thead>
                                <tr><th>Supplier_ID</th><th>Tên Nhà cung cấp/Đội xe</th><th>Email_Contact</th><th>Số điện thoại</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>SUP001</td><td>Đội xe A</td><td><EMAIL></td><td>0901234567</td></tr>
                                <tr><td>SUP002</td><td>Nhà cung cấp B</td><td><EMAIL></td><td>0907654321</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mb-5">
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Tab: `Master_Vehicles`</h4>
                        <p class="text-gray-700 mb-2">
                            Thêm một tab mới và đổi tên thành `Master_Vehicles`.
                            Nhập các tiêu đề cột sau vào Dòng 1 và thêm dữ liệu mẫu:
                        </p>
                        <pre><code>Vehicle_ID | Biển số xe | Loại xe | Thuộc_Supplier_ID</code></pre>
                        <table class="example-table mt-3">
                            <thead>
                                <tr><th>Vehicle_ID</th><th>Biển số xe</th><th>Loại xe</th><th>Thuộc_Supplier_ID</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>VEH001</td><td>51F-123.45</td><td>Xe tải 5 tấn</td><td>SUP001</td></tr>
                                <tr><td>VEH002</td><td>50H-987.65</td><td>Xe tải 10 tấn</td><td>SUP001</td></tr>
                                <tr><td>VEH003</td><td>60C-111.22</td><td>Xe tải 8 tấn</td><td>SUP002</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Tab: `Master_GroupLine`</h4>
                        <p class="text-gray-700 mb-2">
                            Thêm một tab mới và đổi tên thành `Master_GroupLine`.
                            Nhập các tiêu đề cột sau vào Dòng 1 và thêm dữ liệu mẫu (email thật):
                        </p>
                        <pre><code>GroupLine_ID | Tên Group Line | Email_Contact</code></pre>
                        <table class="example-table mt-3">
                            <thead>
                                <tr><th>GroupLine_ID</th><th>Tên Group Line</th><th>Email_Contact</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>GL001</td><td>Vận chuyển Bắc</td><td><EMAIL></td></tr>
                                <tr><td>GL002</td><td>Vận chuyển Nam</td><td><EMAIL></td></tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 2.3: Chia sẻ Google Sheet</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Trong Google Sheet, click vào nút <strong class="font-medium text-gray-800">"Share"</strong> ở góc trên bên phải.</li>
                        <li>Đảm bảo rằng tài khoản Google được sử dụng để tạo AppSheet có quyền <strong class="font-medium text-gray-800">"Editor"</strong> (Chỉnh sửa) đối với Google Sheet này.</li>
                        <li>Nếu triển khai cho nhiều người dùng, hãy chia sẻ cho tất cả các thành viên liên quan và đảm bảo họ có quyền truy cập phù hợp.</li>
                    </ul>
                    <div class="mt-4">
                        <img src="https://placehold.co/600x300/a3e635/0f172a?text=Chia+sẻ+Google+Sheet" alt="Chia sẻ Google Sheet" class="w-full h-auto rounded-md shadow-sm">
                        <p class="text-sm text-gray-500 mt-2 text-center">
                            [Hình ảnh: Màn hình chia sẻ Google Sheet, chọn quyền Editor]
                        </p>
                    </div>
                </li>
            </ol>
        </div>

        <!-- Section 3: Tạo ứng dụng AppSheet mới -->
        <div class="p-6 bg-blue-50 rounded-lg shadow-sm border border-blue-200 mb-10">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">3. Tạo ứng dụng AppSheet mới</h2>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li class="step-card">
                    <h3 class="step-heading">Bước 3.1: Truy cập AppSheet</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Mở trình duyệt và truy cập <a href="https://www.appsheet.com/" target="_blank" class="text-blue-600 hover:underline font-medium">AppSheet.com</a>.</li>
                        <li>Đăng nhập bằng tài khoản Google đã dùng để tạo Google Sheet.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 3.2: Bắt đầu tạo ứng dụng</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Trên trang chủ AppSheet, click vào <strong class="font-medium text-gray-800">"Make a new app"</strong> (Tạo ứng dụng mới).</li>
                        <li>Chọn <strong class="font-medium text-gray-800">"Start from your data"</strong> (Bắt đầu từ dữ liệu của bạn).</li>
                        <li>Đặt tên cho ứng dụng của bạn, ví dụ: <strong class="font-medium text-gray-800">`CP SEEDS Logistics`</strong>.</li>
                        <li>Chọn Category (Danh mục) phù hợp, ví dụ: `Supply Chain` hoặc `Operations`.</li>
                        <li>Click <strong class="font-medium text-gray-800">"Choose your data"</strong> (Chọn dữ liệu của bạn).</li>
                        <li>Chọn <strong class="font-medium text-gray-800">"Google Sheets"</strong> làm nguồn dữ liệu.</li>
                        <li>Tìm và chọn Google Sheet đã tạo ở Bước 2.1 (`CP SEEDS Logistics App Data`).</li>
                        <li>Click <strong class="font-medium text-gray-800">"Select"</strong> (Chọn).</li>
                    </ul>
                    <div class="mt-4">
                        <img src="https://placehold.co/600x300/a3e635/0f172a?text=Tạo+ứng+dụng+AppSheet+mới" alt="Tạo ứng dụng AppSheet mới" class="w-full h-auto rounded-md shadow-sm">
                        <p class="text-sm text-gray-500 mt-2 text-center">
                            [Hình ảnh: Màn hình tạo ứng dụng mới trong AppSheet, chọn Google Sheet]
                        </p>
                    </div>
                </li>
            </ol>
        </div>

        <!-- Section 4: Cấu hình bảng dữ liệu (Data Tables) -->
        <div class="p-6 bg-blue-50 rounded-lg shadow-sm border border-blue-200 mb-10">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">4. Cấu hình bảng dữ liệu (Data Tables)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong trình chỉnh sửa AppSheet (editor), điều hướng đến mục **"Data" > "Tables"** ở thanh điều hướng bên trái.
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li class="step-card">
                    <h3 class="step-heading">Bước 4.1: Thêm tất cả các Sheet vào AppSheet</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>AppSheet có thể chỉ thêm tab đầu tiên. Click vào <strong class="font-medium text-gray-800">"+ Add New Table"</strong> (Thêm bảng mới).</li>
                        <li>Chọn <strong class="font-medium text-gray-800">"Google Sheets"</strong>, sau đó chọn file Google Sheet của bạn.</li>
                        <li>Lần lượt chọn các tab còn lại (`Planning_Schedule`, `Master_Zones`, `Master_Suppliers`, `Master_Vehicles`, `Master_GroupLine`) và click <strong class="font-medium text-gray-800">"Add this table"</strong>.</li>
                        <li>Đảm bảo tất cả 6 bảng đều xuất hiện trong danh sách Tables.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 4.2: Cấu hình chi tiết từng cột trong mỗi bảng</h3>
                    <p class="text-gray-700 mb-3">
                        Click vào từng bảng trong danh sách Tables, sau đó chọn tab **"Columns"** để chỉnh sửa các cột.
                    </p>

                    <div class="mb-5">
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Bảng: `Har_Plan_Input`</h4>
                        <ul class="list-disc list-inside sub-step space-y-2">
                            <li>`ID_Har_Plan`:
                                <ul>
                                    <li>`Type`: `Text`</li>
                                    <li>Check `Key` (Khóa chính)</li>
                                    <li>Check `Label` (Nhãn)</li>
                                    <li>`Initial Value`: `UNIQUEID()`</li>
                                    <li>`Show?`: ON</li>
                                </ul>
                            </li>
                            <li>`Ngày thu hoạch`: `Type`: `Date`</li>
                            <li>`Số lượng dự kiến (Kg)`: `Type`: `Number`</li>
                            <li>Các cột khác: `Type`: `Text`</li>
                            <li>`Editable?`: Cấu hình quyền chỉnh sửa tùy theo quy trình nhập liệu của Bộ phận Khuyến nông.</li>
                        </ul>
                    </div>

                    <div class="mb-5">
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Bảng: `Planning_Schedule`</h4>
                        <ul class="list-disc list-inside sub-step space-y-2">
                            <li>`ID_Planning_Schedule`:
                                <ul>
                                    <li>`Type`: `Text`</li>
                                    <li>Check `Key`, `Label`</li>
                                    <li>`Initial Value`: `UNIQUEID()`</li>
                                </ul>
                            </li>
                            <li>`ID_Har_Plan_Ref`:
                                <ul>
                                    <li>`Type`: `Ref`</li>
                                    <li>`Source table`: `Har_Plan_Input`</li>
                                    <li>**Quan trọng:** KHÔNG check `Is a part of?`</li>
                                </ul>
                            </li>
                            <li>`Ngày vận chuyển`: `Type`: `Date`</li>
                            <li>`Zone`:
                                <ul>
                                    <li>`Type`: `Ref`</li>
                                    <li>`Source table`: `Master_Zones`</li>
                                </ul>
                            </li>
                            <li>`Đội xe/Nhà cung cấp được giao`:
                                <ul>
                                    <li>`Type`: `Ref`</li>
                                    <li>`Source table`: `Master_Suppliers`</li>
                                </ul>
                            </li>
                            <li>`Số lượng dự kiến (Kg)`: `Type`: `Number`</li>
                            <li>`Biển số xe`: `Type`: `Text` (Hoặc `Ref` tới `Master_Vehicles` nếu cần chọn từ danh sách).</li>
                            <li>`Tên tài xế`: `Type`: `Text`</li>
                            <li>`Số lượng thực tế thu hoạch (Kg)`: `Type`: `Number`</li>
                            <li>`Ghi chú Đội xe`: `Type`: `Text`</li>
                            <li>`Trạng thái`:
                                <ul>
                                    <li>`Type`: `Enum`</li>
                                    <li>Click vào biểu tượng bút chì để chỉnh sửa "Values". Thêm các giá trị: `New`, `Đã phân công`, `Đã cập nhật thông tin xe`, `Cần sửa đổi`, `Đã duyệt`, `Đã hoàn thành`.</li>
                                </ul>
                            </li>
                            <li>`Ghi chú Kế hoạch`: `Type`: `Text`</li>
                        </ul>
                    </div>

                    <div class="mb-5">
                        <h4 class="text-lg font-medium text-gray-800 mb-2">Các bảng `Master_Data` (`Master_Zones`, `Master_Suppliers`, `Master_Vehicles`, `Master_GroupLine`)</h4>
                        <ul class="list-disc list-inside sub-step space-y-2">
                            <li>Đối với mỗi bảng này, cột `_ID` (ví dụ: `Zone_ID`, `Supplier_ID`, v.v.):
                                <ul>
                                    <li>`Type`: `Text`</li>
                                    <li>Check `Key`, `Label`</li>
                                    <li>`Initial Value`: `UNIQUEID()`</li>
                                </ul>
                            </li>
                            <li>Cột `Email_Contact` trong `Master_Suppliers` và `Master_GroupLine`: `Type`: `Email`.</li>
                            <li>Cột `Thuộc_Supplier_ID` trong `Master_Vehicles`: `Type`: `Ref`, `Source table`: `Master_Suppliers`.</li>
                            <li>Đảm bảo các cột khác đúng loại dữ liệu.</li>
                        </ul>
                    </div>
                </li>
            </ol>
        </div>

        <!-- Section 5: Thiết kế UX (Giao diện người dùng) -->
        <div class="p-6 bg-blue-50 rounded-lg shadow-sm border border-blue-200 mb-10">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">5. Thiết kế UX (Giao diện người dùng)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục **"UX" > "Views"** của AppSheet, cấu hình các màn hình cho ứng dụng.
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li class="step-card">
                    <h3 class="step-heading">Bước 5.1: Tạo Views cho Bộ phận Kế hoạch</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>
                            <strong class="font-medium text-gray-800">View: `Lịch Thu Hoạch Mới`</strong>
                            <ul>
                                <li>Tạo View mới. `Data`: `Har_Plan_Input`. `View type`: `Table` hoặc `Deck`. `View name`: `Lịch Thu Hoạch Mới`.</li>
                                <li>`View options > For this view, allow new adds?`: Bỏ chọn.</li>
                                <li>`View options > For this view, allow edits?`: Bỏ chọn.</li>
                                <li>`View options > For this view, allow deletes?`: Bỏ chọn.</li>
                                <li>`Position`: `Left` hoặc `Right`.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="font-medium text-gray-800">View: `Quản lý Kế Hoạch Vận Chuyển`</strong>
                            <ul>
                                <li>Tạo View mới. `Data`: `Planning_Schedule`. `View type`: `Table` hoặc `Deck`. `View name`: `Quản lý Kế Hoạch Vận Chuyển`.</li>
                                <li>`Allow updates`: Check `Adds`, `Deletes`, `Edits`.</li>
                                <li>`Position`: Chọn vị trí.</li>
                                <li>**Form cho `Planning_Schedule`**: Các trường `Zone` và `Đội xe/Nhà cung cấp được giao` sẽ hiển thị dưới dạng danh sách thả xuống.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="font-medium text-gray-800">View: `Lịch Đã Duyệt (Kế hoạch)`</strong>
                            <ul>
                                <li>Tạo View mới. `Data`: `Planning_Schedule`. `View type`: `Table` hoặc `Deck`. `View name`: `Lịch Đã Duyệt (Kế hoạch)`.</li>
                                <li>`For this view`: Chỉ `Read-Only`.</li>
                                <li>`Filter`: `[Trạng thái] = "Đã duyệt"`.</li>
                            </ul>
                        </li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 5.2: Tạo Views cho Đội xe/Nhà cung cấp</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>
                            <strong class="font-medium text-gray-800">View: `Lịch Trình Của Tôi`</strong>
                            <ul>
                                <li>Tạo View mới. `Data`: `Planning_Schedule`. `View type`: `Deck` hoặc `Detail`. `View name`: `Lịch Trình Của Tôi`.</li>
                                <li>`Allow updates`: Chỉ check `Edits`.</li>
                                <li>`Position`: `Ref` (để ẩn khỏi menu chính, hiển thị khi được liên kết qua Action).</li>
                                <li>**Security Filter (Rất quan trọng):**
                                    <br>Vào **"Data" > "Tables"**. Click vào bảng `Planning_Schedule`.
                                    <br>Trong tab **"Security Filter"**, nhập công thức:
                                    <pre><code>[Đội xe/Nhà cung cấp được giao].[Email_Contact] = USEREMAIL()</code></pre>
                                </li>
                                <li>**Giới hạn chỉnh sửa cột:**
                                    <br>Vào `Data > Tables > Planning_Schedule > Columns`. Click vào từng cột `Biển số xe`, `Tên tài xế`, `Số lượng thực tế thu hoạch (Kg)`, `Ghi chú Đội xe`.
                                    <br>Trong phần `Update Behavior > Editable If`, nhập công thức:
                                    <pre><code>AND(
  IN(USEREMAIL(), SELECT(Master_Suppliers[Email_Contact], TRUE)),
  OR([Trạng thái] = "Đã phân công", [Trạng thái] = "Cần sửa đổi")
)</code></pre>
                                    Các cột khác trong `Planning_Schedule` nên là `Read-Only` cho nhóm này.
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 5.3: Tạo Views cho Group Line & Bộ phận Nhà máy</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>
                            <strong class="font-medium text-gray-800">View: `Lịch Vận Chuyển Đã Duyệt`</strong>
                            <ul>
                                <li>Tạo View mới. `Data`: `Planning_Schedule`. `View type`: `Table` hoặc `Deck`. `View name`: `Lịch Vận Chuyển Đã Duyệt`.</li>
                                <li>`For this view`: Chỉ `Read-Only`.</li>
                                <li>**Security Filter (Quan trọng):**
                                    <br>Vào `Data > Tables > Planning_Schedule`. Trong tab **"Security Filter"**, thêm công thức:
                                    <pre><code>[Trạng thái] = "Đã duyệt"</code></pre>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 6: Triển khai Actions (Hành động) -->
        <div class="p-6 bg-blue-50 rounded-lg shadow-sm border border-blue-200 mb-10">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">6. Triển khai Actions (Hành động)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục **"Behavior" > "Actions"**, tạo các nút hành động trên bảng `Planning_Schedule`.
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li class="step-card">
                    <h3 class="step-heading">Bước 6.1: Action: `BPKH - Phân công` (Bộ phận Kế hoạch)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Tạo Action mới. `For a record of this table`: `Planning_Schedule`.</li>
                        <li>`Do this`: `Set the values of some columns in this row`.</li>
                        <li>`Set these columns`: `Trạng thái` = `"Đã phân công"`.</li>
                        <li>`Only show this action when`: `OR([Trạng thái] = "New", [Trạng thái] = "Cần sửa đổi")`.</li>
                        <li>`Appearance > Display name`: `Phân công vận chuyển`.</li>
                        <li>`Placement`: `Overlay` hoặc `Inline`.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 6.2: Action: `DX_NCC - Đã cập nhật thông tin xe` (Đội xe/Nhà cung cấp)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Tạo Action mới. `For a record of this table`: `Planning_Schedule`.</li>
                        <li>`Do this`: `Set the values of some columns in this row`.</li>
                        <li>`Set these columns`: `Trạng thái` = `"Đã cập nhật thông tin xe"`.</li>
                        <li>`Only show this action when`: `OR([Trạng thái] = "Đã phân công", [Trạng thái] = "Cần sửa đổi")`.</li>
                        <li>`Appearance > Display name`: `Xác nhận đã cập nhật xe`.</li>
                        <li>`Placement`: `Overlay` hoặc `Inline`.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 6.3: Action: `BPKH - Duyệt` (Bộ phận Kế hoạch)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Tạo Action mới. `For a record of this table`: `Planning_Schedule`.</li>
                        <li>`Do this`: `Set the values of some columns in this row`.</li>
                        <li>`Set these columns`: `Trạng thái` = `"Đã duyệt"`.</li>
                        <li>`Only show this action when`: `[Trạng thái] = "Đã cập nhật thông tin xe"`.</li>
                        <li>`Appearance > Display name`: `Duyệt Kế hoạch`.</li>
                        <li>`Placement`: `Overlay` hoặc `Inline`.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 6.4: Action: `BPKH - Yêu cầu sửa đổi` (Bộ phận Kế hoạch)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Tạo Action mới. `For a record of this table`: `Planning_Schedule`.</li>
                        <li>`Do this`: `Data: set the values of some columns in this row and then set a custom form to display`.</li>
                        <li>`Set these columns`: `Trạng thái` = `"Cần sửa đổi"`.</li>
                        <li>`Custom form`: Chọn Form mặc định của `Planning_Schedule`.</li>
                        <li>`Only show this action when`: `[Trạng thái] = "Đã cập nhật thông tin xe"`.</li>
                        <li>`Appearance > Display name`: `Yêu cầu sửa đổi`.</li>
                        <li>`Placement`: `Overlay` hoặc `Inline`.</li>
                    </ul>
                    <p class="text-gray-700 mt-2">
                        **Để hiển thị `Ghi chú Kế hoạch` trong Form khi Action này được kích hoạt:**
                        <br>Vào `Data > Tables > Planning_Schedule > Columns`.
                        <br>Click vào cột `Ghi chú Kế hoạch`.
                        <br>Trong phần `Display > Show If`, nhập công thức:
                        <pre><code>CONTEXT("View") = "Planning_Schedule_Form" AND [_THISROW_BEFORE].[Trạng thái] = "Đã cập nhật thông tin xe" AND [_THISROW_AFTER].[Trạng thái] = "Cần sửa đổi"</code></pre>
                    </p>
                </li>
            </ol>
        </div>

        <!-- Section 7: Thiết lập Automations (Tự động hóa) -->
        <div class="p-6 bg-blue-50 rounded-lg shadow-sm border border-blue-200 mb-10">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">7. Thiết lập Automations (Tự động hóa)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục **"Automation" > "Bots"** của AppSheet, tạo các bot để gửi thông báo tự động (email) khi có sự thay đổi trạng thái.
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li class="step-card">
                    <h3 class="step-heading">Bước 7.1: Bot: `Notify_Supplier_Assigned` (Thông báo cho Đội xe/Nhà cung cấp)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Tạo Bot mới.</li>
                        <li>`Event` (Sự kiện):
                            <ul>
                                <li>`Configure Event`: `Data Change`</li>
                                <li>`Table`: `Planning_Schedule`</li>
                                <li>`Change Type`: `Adds and Updates`</li>
                                <li>`Condition`: `[Trạng thái] = "Đã phân công"`</li>
                            </ul>
                        </li>
                        <li>`Process` (Quy trình):
                            <ul>
                                <li>`Configure Process`: `Send an email`</li>
                                <li>`To`: `[Đội xe/Nhà cung cấp được giao].[Email_Contact]`</li>
                                <li>`Subject`: `Lịch trình vận chuyển mới: [Ngày vận chuyển]`</li>
                                <li>`Body`: Tạo email template để hiển thị chi tiết lịch trình.</li>
                            </ul>
                        </li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 7.2: Bot: `Notify_Planning_Updated_By_Supplier` (Thông báo cho Bộ phận Kế hoạch)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Tạo Bot mới.</li>
                        <li>`Event`: `Data Change`, `Table`: `Planning_Schedule`, `Change Type`: `Updates only`, `Condition`: `[Trạng thái] = "Đã cập nhật thông tin xe"`.</li>
                        <li>`Process`: `Send an email`. `To`: Email của Bộ phận Kế hoạch (ví dụ: `<EMAIL>`). `Subject`: `Cần duyệt: Lịch trình vận chuyển đã được cập nhật`. `Body`: Thông báo có lịch trình cần duyệt.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 7.3: Bot: `Notify_GroupLine_Approved` (Thông báo cho Group Line & Nhà máy)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Tạo Bot mới.</li>
                        <li>`Event`: `Data Change`, `Table`: `Planning_Schedule`, `Change Type`: `Updates only`, `Condition`: `[Trạng thái] = "Đã duyệt"`.</li>
                        <li>`Process`: `Send an email`. `To`: `SELECT(Master_GroupLine[Email_Contact], TRUE)` (và/hoặc email Bộ phận Nhà máy). `Subject`: `Lịch vận chuyển đã duyệt: [Ngày vận chuyển]`. `Body`: Chi tiết lịch trình đã duyệt.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 7.4: Bot: `Notify_Supplier_Need_Revision` (Thông báo cho Đội xe/Nhà cung cấp yêu cầu sửa đổi)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Tạo Bot mới.</li>
                        <li>`Event`: `Data Change`, `Table`: `Planning_Schedule`, `Change Type`: `Updates only`, `Condition`: `[Trạng thái] = "Cần sửa đổi"`.</li>
                        <li>`Process`: `Send an email`. `To`: `[Đội xe/Nhà cung cấp được giao].[Email_Contact]`. `Subject`: `Yêu cầu sửa đổi: Lịch trình vận chuyển`. `Body`: Thông báo yêu cầu sửa đổi, kèm theo nội dung cột `Ghi chú Kế hoạch`.</li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 8: Cấu hình bảo mật (Security) -->
        <div class="p-6 bg-blue-50 rounded-lg shadow-sm border border-blue-200 mb-10">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">8. Cấu hình bảo mật (Security)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục **"Security"** của AppSheet, thiết lập quyền truy cập cho từng nhóm người dùng.
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li class="step-card">
                    <h3 class="step-heading">Bước 8.1: Authentication (Xác thực người dùng)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>`Authentication provider`: Chọn **"Google"**.</li>
                        <li>`Require sign-in`: Check `ON`.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 8.2: Phân quyền người dùng (User Roles)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>
                            <strong class="font-medium text-gray-800">Bộ phận Kế hoạch:</strong>
                            <ul>
                                <li>Trong mục `Security > Users`, thêm email của những người thuộc Bộ phận Kế hoạch vào danh sách "Users".</li>
                                <li>Họ sẽ có quyền truy cập đầy đủ vào các bảng đã thêm.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="font-medium text-gray-800">Đội xe/Nhà cung cấp:</strong>
                            <ul>
                                <li>Trong mục `Security > Users`, thêm email của Đội xe/Nhà cung cấp.</li>
                                <li>**Security Filter trên `Planning_Schedule`**:
                                    <br>Vào `Data > Tables > Planning_Schedule`. Trong tab **"Security Filter"**, nhập công thức:
                                    <pre><code>[Đội xe/Nhà cung cấp được giao].[Email_Contact] = USEREMAIL()</code></pre>
                                </li>
                                <li>**Quyền chỉnh sửa cột cụ thể**: (Đã làm ở Bước 5.2)</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="font-medium text-gray-800">Group Line & Bộ phận Nhà máy:</strong>
                            <ul>
                                <li>Trong mục `Security > Users`, thêm email của Group Line và Bộ phận Nhà máy.</li>
                                <li>**Security Filter trên `Planning_Schedule`**:
                                    <br>Vào `Data > Tables > Planning_Schedule`. Trong tab **"Security Filter"**, thêm công thức:
                                    <pre><code>[Trạng thái] = "Đã duyệt"</code></pre>
                                </li>
                                <li>`Data > Tables > Planning_Schedule`: Đặt `Are updates allowed?` là `Read-Only` cho toàn bộ bảng đối với nhóm này.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 9: Triển khai và Kiểm thử -->
        <div class="p-6 bg-blue-50 rounded-lg shadow-sm border border-blue-200 mb-10">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">9. Triển khai và Kiểm thử</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Sau khi cấu hình xong, tiến hành triển khai và kiểm tra ứng dụng.
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li class="step-card">
                    <h3 class="step-heading">Bước 9.1: Kiểm tra lỗi triển khai (Deployment Check)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Ở thanh điều hướng bên trái, click vào biểu tượng <strong class="font-medium text-gray-800">"Deployment"</strong>.</li>
                        <li>Click vào <strong class="font-medium text-gray-800">"Deployment Check"</strong> để tìm lỗi hoặc cảnh báo. Khắc phục các vấn đề được báo cáo.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 9.2: Triển khai ứng dụng (Deploy)</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Chuyển trạng thái ứng dụng từ `Prototype` sang `Deployed`.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 9.3: Chia sẻ ứng dụng với người dùng</h3>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>Quay lại mục <strong class="font-medium text-gray-800">"Security" > "Users"</strong>.</li>
                        <li>Thêm email của tất cả những người dùng vào danh sách.</li>
                        <li>Hướng dẫn người dùng cài đặt ứng dụng AppSheet trên điện thoại (từ App Store/Google Play) và đăng nhập bằng tài khoản Google đã được mời. Hoặc truy cập qua trình duyệt web.</li>
                    </ul>
                </li>

                <li class="step-card">
                    <h3 class="step-heading">Bước 9.4: Kiểm thử chi tiết</h3>
                    <p class="text-gray-700 mb-3">
                        Thực hiện kiểm thử kỹ lưỡng để đảm bảo ứng dụng hoạt động đúng theo quy trình nghiệp vụ.
                    </p>
                    <ul class="list-disc list-inside sub-step space-y-2">
                        <li>**Kiểm thử vai trò Bộ phận Kế hoạch:** Đăng nhập, thử tạo lịch trình, phân công, duyệt, yêu cầu sửa đổi.</li>
                        <li>**Kiểm thử vai trò Đội xe/Nhà cung cấp:** Đăng nhập, kiểm tra quyền truy cập (chỉ lịch trình của họ), thử chỉnh sửa thông tin xe, xác nhận cập nhật.</li>
                        <li>**Kiểm thử vai trò Group Line & Bộ phận Nhà máy:** Đăng nhập, kiểm tra quyền truy cập (chỉ lịch trình đã duyệt), đảm bảo không thể chỉnh sửa.</li>
                        <li>**Kiểm tra đồng bộ Google Sheet:** Sau mỗi thao tác trong AppSheet, mở Google Sheet để đảm bảo dữ liệu được cập nhật chính xác và theo thời gian thực.</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>
</body>
</html>
