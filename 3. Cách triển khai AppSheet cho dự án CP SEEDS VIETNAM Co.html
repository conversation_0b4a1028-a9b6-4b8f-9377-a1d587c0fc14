<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hướng dẫn triển khai AppSheet cho dự án CP SEEDS VIETNAM Co.LTD</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* Light gray background */
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #e0e0e0;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* Style for code blocks within HTML content for clarity */
        pre {
            background-color: #e2e8f0; /* bg-gray-200 */
            padding: 1rem;
            border-radius: 0.5rem; /* rounded-lg */
            overflow-x: auto;
            font-family: 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
            font-size: 0.875rem; /* text-sm */
            line-height: 1.5;
        }
    </style>
</head>
<body class="p-4 md:p-8">
    <!-- Main Container -->
    <div class="max-w-4xl mx-auto bg-white shadow-xl rounded-lg p-6 md:p-10 border border-gray-200">
        <!-- Document Title -->
        <h1 class="text-3xl md:text-4xl font-bold text-indigo-800 mb-6 text-center">
            HƯỚNG DẪN TRIỂN KHAI APPSHEET CHO DỰ ÁN CP SEEDS VIETNAM Co.LTD
        </h1>
        <p class="text-gray-600 text-center mb-8 text-lg">
            Xây dựng ứng dụng quản lý lịch thu hoạch và vận chuyển
        </p>

        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <p class="text-gray-700 leading-relaxed">
                Đây là hướng dẫn từng bước để bạn tự xây dựng ứng dụng AppSheet cho dự án CP SEEDS VIETNAM Co.LTD, dựa trên cấu trúc Google Sheet đã thiết kế. Bạn sẽ cần truy cập vào tài khoản Google và AppSheet của mình để thực hiện các bước này.
            </p>
        </div>

        <!-- Section 1: Chuẩn bị Google Sheet -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">1. Chuẩn bị Google Sheet (Cơ sở dữ liệu)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Đảm bảo bạn đã tạo một Google Sheet với các tab (sheets) và cấu trúc cột chính xác như đã mô tả trong tài liệu BRD trước đó:
            </p>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2 pl-4">
                <li><strong class="text-gray-800">`Har_Plan_Input`</strong></li>
                <li><strong class="text-gray-800">`Planning_Schedule`</strong></li>
                <li><strong class="text-gray-800">`Master_Zones`</strong></li>
                <li><strong class="text-gray-800">`Master_Suppliers`</strong> (Bao gồm cột `Email_Contact` cho mỗi nhà cung cấp/đội xe)</li>
                <li><strong class="text-gray-800">`Master_Vehicles`</strong></li>
                <li><strong class="text-gray-800">`Master_GroupLine`</strong> (Bao gồm cột `Email_Contact` cho mỗi group line)</li>
            </ul>
            <p class="text-gray-700 leading-relaxed mt-4">
                Hãy nhập một vài dữ liệu mẫu vào các sheet `Master_Zones`, `Master_Suppliers`, `Master_Vehicles` để tiện cho việc kiểm thử sau này. Đối với `Har_Plan_Input` và `Planning_Schedule`, có thể để trống hoặc nhập một vài dòng giả định.
            </p>
        </div>

        <!-- Section 2: Tạo ứng dụng AppSheet mới -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">2. Tạo ứng dụng AppSheet mới</h2>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-3 pl-4">
                <li>Truy cập vào <a href="https://www.appsheet.com/" target="_blank" class="text-blue-600 hover:underline font-medium">AppSheet.com</a> và đăng nhập bằng tài khoản Google của bạn.</li>
                <li>Chọn <strong class="text-gray-800">"Make a new app"</strong> hoặc <strong class="text-gray-800">"Start from your data"</strong>.</li>
                <li>Chọn Google Sheet mà bạn đã chuẩn bị làm nguồn dữ liệu.</li>
                <li>AppSheet sẽ tự động tạo một ứng dụng cơ bản.</li>
            </ol>
        </div>

        <!-- Section 3: Cấu hình bảng dữ liệu (Data Tables) -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">3. Cấu hình bảng dữ liệu (Data Tables)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục <strong class="text-gray-800">"Data" > "Tables"</strong> của AppSheet:
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-3 pl-4">
                <li>
                    <strong class="text-gray-800">Thêm tất cả các Sheet:</strong> Đảm bảo tất cả các tab (`Har_Plan_Input`, `Planning_Schedule`, `Master_Zones`, `Master_Suppliers`, `Master_Vehicles`, `Master_GroupLine`) đều được thêm vào làm bảng dữ liệu.
                </li>
                <li>
                    <strong class="text-gray-800">Cấu hình từng bảng:</strong> Click vào từng bảng để chỉnh sửa cột (<strong class="text-gray-800">"Columns"</strong>).
                    <ul class="list-disc list-inside ml-5 mt-2 space-y-2">
                        <li>
                            <strong class="text-gray-800">`Har_Plan_Input`</strong>
                            <ul class="list-circle list-inside ml-5">
                                <li>`ID_Har_Plan`: Set là `Key` và `Label`. Loại: `Text`. Công thức khởi tạo (Initial value): `UNIQUEID()`.</li>
                                <li>`Ngày thu hoạch`: Loại: `Date`.</li>
                                <li>`Số lượng dự kiến (Kg)`: Loại: `Number`.</li>
                                <li>Các cột khác: Đảm bảo đúng loại dữ liệu (Text).</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="text-gray-800">`Planning_Schedule`</strong>
                            <ul class="list-circle list-inside ml-5">
                                <li>`ID_Planning_Schedule`: Set là `Key` và `Label`. Loại: `Text`. Công thức khởi tạo: `UNIQUEID()`.</li>
                                <li>`ID_Har_Plan_Ref`: Loại: `Ref`. Source table: `Har_Plan_Input`. Is a part of?: <strong class="text-gray-800">KHÔNG</strong> chọn.</li>
                                <li>`Ngày vận chuyển`: Loại: `Date`.</li>
                                <li>`Zone`: Loại: `Ref`. Source table: `Master_Zones`.</li>
                                <li>`Đội xe/Nhà cung cấp được giao`: Loại: `Ref`. Source table: `Master_Suppliers`.</li>
                                <li>`Số lượng dự kiến (Kg)`: Loại: `Number`.</li>
                                <li>`Biển số xe`: Loại: `Text`. Có thể là `Ref` nếu chỉ chọn từ `Master_Vehicles` và không cho nhập tự do. Nếu cho phép nhập tự do, để `Text`.</li>
                                <li>`Số lượng thực tế thu hoạch (Kg)`: Loại: `Number`.</li>
                                <li>`Trạng thái`: Loại: `Enum`. Values: `New`, `Đã phân công`, `Đã cập nhật thông tin xe`, `Cần sửa đổi`, `Đã duyệt`, `Đã hoàn thành`.</li>
                                <li>Các cột khác: Đảm bảo đúng loại dữ liệu.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="text-gray-800">`Master_Zones`</strong>
                            <ul class="list-circle list-inside ml-5">
                                <li>`Zone_ID`: Set là `Key` và `Label`. Loại: `Text`. Công thức khởi tạo: `UNIQUEID()`.</li>
                                <li>`Tên Zone`: Loại: `Text`.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="text-gray-800">`Master_Suppliers`</strong>
                            <ul class="list-circle list-inside ml-5">
                                <li>`Supplier_ID`: Set là `Key` và `Label`. Loại: `Text`. Công thức khởi tạo: `UNIQUEID()`.</li>
                                <li>`Tên Nhà cung cấp/Đội xe`: Loại: `Text`.</li>
                                <li>`Email_Contact`: Loại: `Email`.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="text-gray-800">`Master_Vehicles`</strong>
                            <ul class="list-circle list-inside ml-5">
                                <li>`Vehicle_ID`: Set là `Key` và `Label`. Loại: `Text`. Công thức khởi tạo: `UNIQUEID()`.</li>
                                <li>`Biển số xe`: Loại: `Text`.</li>
                                <li>`Thuộc_Supplier_ID`: Loại: `Ref`. Source table: `Master_Suppliers`.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="text-gray-800">`Master_GroupLine`</strong>
                            <ul class="list-circle list-inside ml-5">
                                <li>`GroupLine_ID`: Set là `Key` và `Label`. Loại: `Text`. Công thức khởi tạo: `UNIQUEID()`.</li>
                                <li>`Tên Group Line`: Loại: `Text`.</li>
                                <li>`Email_Contact`: Loại: `Email`.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section 4: Thiết kế UX (Giao diện người dùng) -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">4. Thiết kế UX (Giao diện người dùng)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục <strong class="text-gray-800">"UX" > "Views"</strong> của AppSheet:
            </p>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li>
                    <strong class="text-gray-800 text-lg">Cho Bộ phận Kế hoạch:</strong>
                    <ul class="list-circle list-inside ml-5 space-y-2">
                        <li>
                            <strong class="text-gray-800">View: `Lịch Thu Hoạch Mới`</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Data: `Har_Plan_Input`</li>
                                <li>View type: `Table` hoặc `Deck` (chọn loại hiển thị phù hợp).</li>
                                <li>For this view: `Read-Only` (chỉ đọc).</li>
                                <li>Primary action: Tạo một action để "Tạo kế hoạch vận chuyển" từ dòng `Har_Plan_Input` và dẫn đến form thêm mới `Planning_Schedule` với các trường đã được điền sẵn.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="text-gray-800">View: `Quản lý Kế Hoạch Vận Chuyển`</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Data: `Planning_Schedule`</li>
                                <li>View type: `Table` hoặc `Deck`.</li>
                                <li>Allow updates: `Add`, `Edit`.</li>
                                <li>Tạo một slice (`Planning_Schedule_BPKH`) để lọc dữ liệu chỉ cho BPKH nếu cần quản lý nhiều trạng thái khác nhau.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="text-gray-800">View: `Lịch Đã Duyệt`</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Data: `Planning_Schedule` (hoặc một slice lọc `[Trạng thái] = "Đã duyệt"`)</li>
                                <li>View type: `Table` hoặc `Deck`.</li>
                                <li>For this view: `Read-Only`.</li>
                            </ul>
                        </li>
                        <li><strong class="text-gray-800">Form cho `Planning_Schedule`:</strong> Khi tạo hoặc chỉnh sửa một mục `Planning_Schedule`, đảm bảo các trường `Zone`, `Đội xe/Nhà cung cấp được giao` là `Ref` và cho phép chọn từ danh sách.</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Cho Đội xe/Nhà cung cấp:</strong>
                    <ul class="list-circle list-inside ml-5 space-y-2">
                        <li>
                            <strong class="text-gray-800">View: `Lịch Trình Của Tôi`</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Data: `Planning_Schedule`</li>
                                <li>View type: `Deck` hoặc `Detail` (để dễ xem trên điện thoại).</li>
                                <li>**Security Filter:** Áp dụng bộ lọc bảo mật để chỉ hiển thị các lịch trình của nhà cung cấp đó:
                                    <pre><code>LOOKUP(USEREMAIL(), "Master_Suppliers", "Email_Contact", "Supplier_ID") = [Đội xe/Nhà cung cấp được giao]</code></pre>
                                    (Giả sử `[Đội xe/Nhà cung cấp được giao]` lưu `Supplier_ID`)
                                </li>
                                <li>Allow updates: `Edit` (chỉ cho phép chỉnh sửa các trường như `Biển số xe`, `Tên tài xế`, `Số lượng thực tế thu hoạch`, `Ghi chú Đội xe`).</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Cho Group Line & Bộ phận Nhà máy:</strong>
                    <ul class="list-circle list-inside ml-5 space-y-2">
                        <li>
                            <strong class="text-gray-800">View: `Lịch Vận Chuyển Đã Duyệt`</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Data: `Planning_Schedule`</li>
                                <li>View type: `Table` hoặc `Deck`.</li>
                                <li>**Security Filter:** Chỉ hiển thị các lịch trình đã duyệt:
                                    <pre><code>[Trạng thái] = "Đã duyệt"</code></pre>
                                </li>
                                <li>For this view: `Read-Only`.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Section 5: Triển khai Actions -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">5. Triển khai Actions (Hành động)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục <strong class="text-gray-800">"Behavior" > "Actions"</strong> của AppSheet, tạo các action sau trên bảng `Planning_Schedule`:
            </p>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li>
                    <strong class="text-gray-800 text-lg">Action: `BPKH - Phân công`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li>Do this: `Set the values of some columns in this row`.</li>
                        <li>Set these columns: `Trạng thái` = `"Đã phân công"`.</li>
                        <li>Only show this action when: `[Trạng thái] = "New"` hoặc `[Trạng thái] = "Cần sửa đổi"`.</li>
                        <li>Placement: `Inline` (xuất hiện trên dòng dữ liệu).</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Action: `DX_NCC - Đã cập nhật thông tin xe`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li>Do this: `Set the values of some columns in this row`.</li>
                        <li>Set these columns: `Trạng thái` = `"Đã cập nhật thông tin xe"`.</li>
                        <li>Only show this action when: `[Trạng thái] = "Đã phân công"` hoặc `[Trạng thái] = "Cần sửa đổi"`.</li>
                        <li>Placement: `Inline`.</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Action: `BPKH - Duyệt`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li>Do this: `Set the values of some columns in this row`.</li>
                        <li>Set these columns: `Trạng thái` = `"Đã duyệt"`.</li>
                        <li>Only show this action when: `[Trạng thái] = "Đã cập nhật thông tin xe"`.</li>
                        <li>Placement: `Inline`.</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Action: `BPKH - Gửi đến Group Line`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li>Do this: `Data: execute an action on a set of rows`.</li>
                        <li>Target Table: `Planning_Schedule`.</li>
                        <li>Rows to act on: `[_THISROW]`.</li>
                        <li>The action to be executed: Tạo một Action riêng biệt cho việc gửi email/thông báo (xem phần Automation).</li>
                        <li>Only show this action when: `[Trạng thái] = "Đã duyệt"`.</li>
                        <li>Placement: `Inline`.</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Action: `BPKH - Yêu cầu sửa đổi`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li>Do this: `Set the values of some columns in this row`.</li>
                        <li>Set these columns: `Trạng thái` = `"Cần sửa đổi"`, và cho phép nhập `Ghi chú Kế hoạch` (có thể dùng một Action type `Data: set the values of some columns in this row and then set a custom form to display` để mở form nhập ghi chú).</li>
                        <li>Only show this action when: `[Trạng thái] = "Đã cập nhật thông tin xe"`.</li>
                        <li>Placement: `Inline`.</li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Section 6: Thiết lập Automations (Tự động hóa) -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">6. Thiết lập Automations (Tự động hóa)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục <strong class="text-gray-800">"Automation" > "Bots"</strong> của AppSheet, tạo các bot sau:
            </p>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li>
                    <strong class="text-gray-800 text-lg">Bot: `Notify_Supplier_Assigned`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li><strong class="text-gray-800">Event:</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Type: `Data Change`</li>
                                <li>Table: `Planning_Schedule`</li>
                                <li>Changes: `Updates only`</li>
                                <li>Condition: `[Trạng thái] = "Đã phân công"`</li>
                            </ul>
                        </li>
                        <li><strong class="text-gray-800">Process:</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Step: `Send an email`</li>
                                <li>To: `[Đội xe/Nhà cung cấp được giao].[Email_Contact]` (sử dụng biểu thức `LOOKUP()` nếu `[Đội xe/Nhà cung cấp được giao]` là Supplier_ID)</li>
                                <li>Subject: `[Ngày vận chuyển]: Lịch trình vận chuyển mới từ CP SEEDS`</li>
                                <li>Body: Sử dụng template để hiển thị thông tin chi tiết lịch trình.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Bot: `Notify_Planning_Updated_By_Supplier`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li><strong class="text-gray-800">Event:</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Type: `Data Change`</li>
                                <li>Table: `Planning_Schedule`</li>
                                <li>Changes: `Updates only`</li>
                                <li>Condition: `[Trạng thái] = "Đã cập nhật thông tin xe"`</li>
                            </ul>
                        </li>
                        <li><strong class="text-gray-800">Process:</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Step: `Send an email`</li>
                                <li>To: Email của Bộ phận Kế hoạch (có thể là một email cố định, ví dụ: `<EMAIL>`).</li>
                                <li>Subject: `Cần duyệt: Lịch trình vận chuyển đã được cập nhật`</li>
                                <li>Body: Thông báo có lịch trình cần duyệt kèm link truy cập.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Bot: `Notify_GroupLine_Approved`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li><strong class="text-gray-800">Event:</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Type: `Data Change`</li>
                                <li>Table: `Planning_Schedule`</li>
                                <li>Changes: `Updates only`</li>
                                <li>Condition: `[Trạng thái] = "Đã duyệt"`</li>
                            </ul>
                        </li>
                        <li><strong class="text-gray-800">Process:</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Step: `Send an email`</li>
                                <li>To: `[Master_GroupLine].[Email_Contact]` (dùng SELECT để lấy tất cả email Group Line), và email Bộ phận Nhà máy nếu có.</li>
                                <li>Subject: `Lịch vận chuyển đã duyệt: [Ngày vận chuyển]`</li>
                                <li>Body: Chi tiết lịch trình đã duyệt.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">Bot: `Notify_Supplier_Need_Revision`</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li><strong class="text-gray-800">Event:</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Type: `Data Change`</li>
                                <li>Table: `Planning_Schedule`</li>
                                <li>Changes: `Updates only`</li>
                                <li>Condition: `[Trạng thái] = "Cần sửa đổi"`</li>
                            </ul>
                        </li>
                        <li><strong class="text-gray-800">Process:</strong>
                            <ul class="list-square list-inside ml-5">
                                <li>Step: `Send an email`</li>
                                <li>To: `[Đội xe/Nhà cung cấp được giao].[Email_Contact]`</li>
                                <li>Subject: `Yêu cầu sửa đổi: Lịch trình vận chuyển`</li>
                                <li>Body: Thông báo yêu cầu sửa đổi kèm `Ghi chú Kế hoạch`.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Section 7: Cấu hình bảo mật (Security) -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">7. Cấu hình bảo mật (Security)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Trong mục <strong class="text-gray-800">"Security"</strong> của AppSheet:
            </p>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-4 pl-4">
                <li>
                    <strong class="text-gray-800 text-lg">Authentication:</strong>
                    <p class="ml-5">Chọn <strong class="text-gray-800">"Google"</strong> làm phương thức xác thực người dùng. Điều này yêu cầu tất cả người dùng phải có tài khoản Google để truy cập ứng dụng.</p>
                </li>
                <li>
                    <strong class="text-gray-800 text-lg">User Roles (Phân quyền người dùng):</strong>
                    <p class="ml-5">AppSheet không có khái niệm "role" cứng nhắc, nhưng bạn có thể tạo các vai trò bằng cách sử dụng các biểu thức trong Security Filters hoặc `Show If` của các View.</p>
                    <ul class="list-circle list-inside ml-5 mt-2 space-y-2">
                        <li>
                            <strong class="text-gray-800">Bộ phận Kế hoạch:</strong>
                            <p class="ml-5">Trong phần "Users", thêm các email của Bộ phận Kế hoạch vào danh sách người dùng ứng dụng. Họ sẽ có quyền truy cập đầy đủ vào các bảng và có thể chỉnh sửa mọi thứ.</p>
                            <ul class="list-square list-inside ml-5">
                                <li>Đặt quyền `Read/Write` cho bảng `Planning_Schedule` và các bảng `Master_Data`.</li>
                            </ul>
                        </li>
                        <li>
                            <strong class="text-gray-800">Đội xe/Nhà cung cấp:</strong>
                            <p class="ml-5">Trong phần "Users", thêm các email của Đội xe/Nhà cung cấp. Áp dụng <strong class="text-gray-800">Security Filter</strong> trên bảng `Planning_Schedule` cho nhóm người dùng này:</p>
                            <pre><code>[Đội xe/Nhà cung cấp được giao].[Email_Contact] = USEREMAIL()</code></pre>
                            <p class="ml-5">Điều này đảm bảo mỗi nhà cung cấp chỉ thấy và chỉnh sửa các lịch trình được giao cho họ. Bạn cũng có thể thiết lập quyền chỉ `Edit` cho các cột cụ thể (`Biển số xe`, `Tên tài xế`, `Số lượng thực tế thu hoạch`, `Ghi chú Đội xe`) và `Read-Only` cho các cột khác.</p>
                        </li>
                        <li>
                            <strong class="text-gray-800">Group Line & Bộ phận Nhà máy:</strong>
                            <p class="ml-5">Trong phần "Users", thêm các email của Group Line và Bộ phận Nhà máy. Áp dụng <strong class="text-gray-800">Security Filter</strong> trên bảng `Planning_Schedule` cho nhóm người dùng này:</p>
                            <pre><code>[Trạng thái] = "Đã duyệt"</code></pre>
                            <p class="ml-5">Và đặt quyền là `Read-Only` cho toàn bộ bảng `Planning_Schedule` đối với nhóm này.</p>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Section 8: Triển khai và Kiểm thử -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">8. Triển khai và Kiểm thử</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Sau khi cấu hình xong các bước trên:
            </p>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2 pl-4">
                <li><strong class="text-gray-800">Deploy ứng dụng:</strong> Chuyển trạng thái ứng dụng từ `Prototype` sang `Deployed` trong AppSheet.</li>
                <li><strong class="text-gray-800">Chia sẻ ứng dụng:</strong> Trong mục <strong class="text-gray-800">"Users" > "Users"</strong>, thêm các email của người dùng vào danh sách để họ có thể truy cập ứng dụng.</li>
                <li><strong class="text-gray-800">Kiểm thử:</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li>Đăng nhập bằng các tài khoản khác nhau (BP Kế hoạch, Đội xe/Nhà cung cấp, Group Line/Nhà máy) để kiểm tra đúng phân quyền và luồng nghiệp vụ.</li>
                        <li>Kiểm tra các automation (email thông báo) có hoạt động đúng không.</li>
                        <li>Đảm bảo dữ liệu được cập nhật chính xác trên Google Sheet.</li>
                    </ul>
                </li>
            </ul>
        </div>

        <p class="text-sm text-gray-500 text-center mt-10">
            Với các bước trên, bạn sẽ có một ứng dụng AppSheet mạnh mẽ để quản lý quy trình vận chuyển của CP SEEDS VIETNAM Co.LTD một cách hiệu quả. Chúc bạn thành công!
        </p>
    </div>
</body>
</html>
