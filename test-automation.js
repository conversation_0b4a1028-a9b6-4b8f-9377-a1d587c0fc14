const CPSeedsAppSheetAutomation = require('./cp-seeds-appsheet-automation');

async function testAutomation() {
    console.log('🧪 Testing CP SEEDS AppSheet Automation...');
    
    const automation = new CPSeedsAppSheetAutomation();
    
    try {
        // Test initialization
        console.log('✅ Testing initialization...');
        await automation.init();
        console.log('✅ Initialization successful');
        
        // Test browser opening
        console.log('✅ Testing browser functionality...');
        await automation.page.goto('https://www.google.com');
        await automation.page.waitForTimeout(2000);
        console.log('✅ Browser navigation successful');
        
        // Close test
        await automation.close();
        console.log('✅ All tests passed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        await automation.close();
        process.exit(1);
    }
}

if (require.main === module) {
    testAutomation();
}
