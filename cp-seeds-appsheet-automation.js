const { chromium } = require('playwright');

class CPSeedsAppSheetAutomation {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.googleSheetUrl = null;
    }

    async init() {
        console.log('🚀 Khởi tạo trình duyệt...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000 // Chậm lại để dễ theo dõi
        });
        this.context = await this.browser.newContext();
        this.page = await this.context.newPage();
    }

    async createGoogleSheet() {
        console.log('📊 Bước 1: Tạo Google Sheet mới...');
        
        // Truy cập Google Sheets
        await this.page.goto('https://docs.google.com/spreadsheets/');
        await this.page.waitForTimeout(3000);

        // Tạo sheet mới
        try {
            await this.page.click('div[aria-label="Blank spreadsheet"]', { timeout: 10000 });
        } catch {
            // Fallback nếu không tìm thấy
            await this.page.click('.docs-homescreen-templates-templateview-preview', { timeout: 10000 });
        }
        
        await this.page.waitForTimeout(5000);

        // Đổi tên sheet
        await this.page.click('input[aria-label="Rename"]', { timeout: 10000 });
        await this.page.fill('input[aria-label="Rename"]', 'CP SEEDS Logistics App Data');
        await this.page.press('input[aria-label="Rename"]', 'Enter');
        
        console.log('✅ Đã tạo Google Sheet: CP SEEDS Logistics App Data');
        
        // Lưu URL của sheet
        this.googleSheetUrl = this.page.url();
        console.log('📋 URL Google Sheet:', this.googleSheetUrl);
    }

    async createSheetTabs() {
        console.log('📝 Bước 2: Tạo các tab và cấu hình headers...');

        const tabs = [
            {
                name: 'Har_Plan_Input',
                headers: ['ID_Har_Plan', 'Ngày thu hoạch', 'Tỉnh', 'Huyện', 'Địa điểm', 'Loại cây trồng', 'Số lượng dự kiến (Kg)', 'Ghi chú Khuyến nông']
            },
            {
                name: 'Planning_Schedule',
                headers: ['ID_Planning_Schedule', 'ID_Har_Plan_Ref', 'Ngày vận chuyển', 'Zone', 'Đội xe/Nhà cung cấp được giao', 'Số lượng dự kiến (Kg)', 'Biển số xe', 'Tên tài xế', 'Số lượng thực tế thu hoạch (Kg)', 'Ghi chú Đội xe', 'Trạng thái', 'Ghi chú Kế hoạch']
            },
            {
                name: 'Master_Zones',
                headers: ['Zone_ID', 'Tên Zone', 'Mô tả'],
                sampleData: [
                    ['ZONE001', 'Miền Bắc', 'Các tỉnh phía Bắc'],
                    ['ZONE002', 'Miền Trung', 'Các tỉnh miền Trung'],
                    ['ZONE003', 'Miền Nam', 'Các tỉnh phía Nam']
                ]
            },
            {
                name: 'Master_Suppliers',
                headers: ['Supplier_ID', 'Tên Nhà cung cấp/Đội xe', 'Email_Contact', 'Số điện thoại'],
                sampleData: [
                    ['SUP001', 'Đội xe A', '<EMAIL>', '0901234567'],
                    ['SUP002', 'Nhà cung cấp B', '<EMAIL>', '0907654321']
                ]
            },
            {
                name: 'Master_Vehicles',
                headers: ['Vehicle_ID', 'Biển số xe', 'Loại xe', 'Thuộc_Supplier_ID'],
                sampleData: [
                    ['VEH001', '51F-123.45', 'Xe tải 5 tấn', 'SUP001'],
                    ['VEH002', '50H-987.65', 'Xe tải 10 tấn', 'SUP001'],
                    ['VEH003', '60C-111.22', 'Xe tải 8 tấn', 'SUP002']
                ]
            },
            {
                name: 'Master_GroupLine',
                headers: ['GroupLine_ID', 'Tên Group Line', 'Email_Contact'],
                sampleData: [
                    ['GL001', 'Vận chuyển Bắc', '<EMAIL>'],
                    ['GL002', 'Vận chuyển Nam', '<EMAIL>']
                ]
            }
        ];

        for (let i = 0; i < tabs.length; i++) {
            const tab = tabs[i];
            
            if (i > 0) {
                // Tạo tab mới (trừ tab đầu tiên)
                await this.page.click('div[aria-label="Add sheet"]');
                await this.page.waitForTimeout(2000);
            }

            // Đổi tên tab
            await this.page.click(`div[aria-label="Sheet${i + 1}"]`, { button: 'right' });
            await this.page.click('span:has-text("Rename")');
            await this.page.fill('input[aria-label="Name"]', tab.name);
            await this.page.press('input[aria-label="Name"]', 'Enter');
            await this.page.waitForTimeout(1000);

            // Thêm headers
            await this.addHeaders(tab.headers);

            // Thêm dữ liệu mẫu nếu có
            if (tab.sampleData) {
                await this.addSampleData(tab.sampleData);
            }

            console.log(`✅ Đã tạo tab: ${tab.name}`);
        }
    }

    async addHeaders(headers) {
        // Click vào cell A1
        await this.page.click('div[aria-label="A1"]');
        
        for (let i = 0; i < headers.length; i++) {
            await this.page.fill('div[aria-label="A1"]', headers[i]);
            if (i < headers.length - 1) {
                await this.page.press('div[aria-label="A1"]', 'Tab');
            }
        }
        await this.page.press('div[aria-label="A1"]', 'Enter');
    }

    async addSampleData(data) {
        for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
            const row = data[rowIndex];
            // Click vào cell đầu tiên của hàng
            await this.page.click(`div[aria-label="A${rowIndex + 2}"]`);
            
            for (let colIndex = 0; colIndex < row.length; colIndex++) {
                await this.page.fill(`div[aria-label="A${rowIndex + 2}"]`, row[colIndex]);
                if (colIndex < row.length - 1) {
                    await this.page.press(`div[aria-label="A${rowIndex + 2}"]`, 'Tab');
                }
            }
            await this.page.press(`div[aria-label="A${rowIndex + 2}"]`, 'Enter');
        }
    }

    async shareGoogleSheet() {
        console.log('🔗 Bước 3: Chia sẻ Google Sheet...');
        
        // Click nút Share
        await this.page.click('button[aria-label="Share. Anyone with the link can view. "]');
        await this.page.waitForTimeout(2000);

        // Thay đổi quyền truy cập
        await this.page.click('button:has-text("Restricted")');
        await this.page.click('span:has-text("Anyone with the link")');
        await this.page.click('button:has-text("Done")');
        
        console.log('✅ Đã chia sẻ Google Sheet với quyền "Anyone with the link"');
    }

    async createAppSheetApp() {
        console.log('🔧 Bước 4: Tạo ứng dụng AppSheet...');
        
        // Mở tab mới cho AppSheet
        const appSheetPage = await this.context.newPage();
        await appSheetPage.goto('https://www.appsheet.com/');
        await appSheetPage.waitForTimeout(3000);

        // Đăng nhập (người dùng cần đăng nhập thủ công)
        console.log('⚠️  Vui lòng đăng nhập vào AppSheet bằng tài khoản Google...');
        await appSheetPage.waitForTimeout(10000);

        // Tạo app mới
        try {
            await appSheetPage.click('text="Make a new app"');
            await appSheetPage.waitForTimeout(3000);

            await appSheetPage.click('text="Start from your data"');
            await appSheetPage.waitForTimeout(2000);

            // Đặt tên app
            await appSheetPage.fill('input[placeholder="App name"]', 'CP SEEDS Logistics');
            
            // Chọn category
            await appSheetPage.selectOption('select', 'Operations');
            
            // Chọn Google Sheets
            await appSheetPage.click('text="Choose your data"');
            await appSheetPage.click('text="Google Sheets"');
            
            console.log('📋 Vui lòng chọn Google Sheet đã tạo:', this.googleSheetUrl);
            console.log('⏳ Đang chờ người dùng chọn sheet...');
            
            // Chờ người dùng chọn sheet
            await appSheetPage.waitForTimeout(30000);
            
        } catch (error) {
            console.log('⚠️  Lỗi khi tạo AppSheet app:', error.message);
            console.log('Vui lòng thực hiện thủ công các bước sau:');
            console.log('1. Truy cập https://www.appsheet.com/');
            console.log('2. Click "Make a new app"');
            console.log('3. Chọn "Start from your data"');
            console.log('4. Đặt tên: "CP SEEDS Logistics"');
            console.log('5. Chọn Google Sheet đã tạo');
        }
    }

    async displayManualSteps() {
        console.log('\n🔧 CÁC BƯỚC CẦN THỰC HIỆN THỦ CÔNG TRONG APPSHEET:');
        console.log('=====================================');
        
        console.log('\n4. CẤU HÌNH BẢNG DỮ LIỆU:');
        console.log('- Vào Data > Tables');
        console.log('- Thêm tất cả các sheet vào AppSheet');
        console.log('- Cấu hình từng cột theo hướng dẫn trong tài liệu');
        
        console.log('\n5. THIẾT KẾ UX:');
        console.log('- Vào UX > Views');
        console.log('- Tạo các view cho từng nhóm người dùng');
        console.log('- Cấu hình Security Filter');
        
        console.log('\n6. TRIỂN KHAI ACTIONS:');
        console.log('- Vào Behavior > Actions');
        console.log('- Tạo các action: Phân công, Duyệt, Yêu cầu sửa đổi');
        
        console.log('\n7. THIẾT LẬP AUTOMATIONS:');
        console.log('- Vào Automation > Bots');
        console.log('- Tạo các bot gửi email thông báo');
        
        console.log('\n8. CẤU HÌNH BẢO MẬT:');
        console.log('- Vào Security');
        console.log('- Thiết lập Authentication và User Roles');
        
        console.log('\n9. TRIỂN KHAI:');
        console.log('- Chạy Deployment Check');
        console.log('- Deploy ứng dụng');
        console.log('- Chia sẻ với người dùng');
    }

    async run() {
        try {
            await this.init();
            
            console.log('🎯 BẮT ĐẦU TRIỂN KHAI APPSHEET CHO CP SEEDS VIETNAM');
            console.log('================================================');
            
            await this.createGoogleSheet();
            await this.createSheetTabs();
            await this.shareGoogleSheet();
            await this.createAppSheetApp();
            
            this.displayManualSteps();
            
            console.log('\n✅ HOÀN THÀNH CÁC BƯỚC TỰ ĐỘNG HÓA!');
            console.log('Vui lòng tiếp tục với các bước thủ công trong AppSheet.');
            
        } catch (error) {
            console.error('❌ Lỗi:', error.message);
        }
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Chạy automation
async function main() {
    const automation = new CPSeedsAppSheetAutomation();
    
    try {
        await automation.run();
        
        // Giữ trình duyệt mở để người dùng tiếp tục
        console.log('\n⏳ Trình duyệt sẽ được giữ mở để bạn tiếp tục...');
        console.log('Nhấn Ctrl+C để đóng khi hoàn thành.');
        
        // Chờ người dùng đóng
        process.on('SIGINT', async () => {
            console.log('\n👋 Đang đóng trình duyệt...');
            await automation.close();
            process.exit(0);
        });
        
    } catch (error) {
        console.error('❌ Lỗi chính:', error);
        await automation.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = CPSeedsAppSheetAutomation;
