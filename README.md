# CP SEEDS Vietnam - AppSheet Automation

Automation script để triển khai hệ thống quản lý lịch thu hoạch và vận chuyển bằng AppSheet cho CP SEEDS VIETNAM Co.LTD.

## 🎯 Mục đích

Script này tự động hóa các bước có thể tự động trong quá trình triển khai AppSheet theo tài liệu hướng dẫn, bao gồm:

- ✅ Tạo Google Sheet với cấu trúc dữ liệu chuẩn
- ✅ Tạo các tab và headers theo yêu cầu
- ✅ Thêm dữ liệu mẫu cho các bảng master
- ✅ Chia sẻ Google Sheet
- ✅ Khởi tạo ứng dụng AppSheet
- 📋 Hướng dẫn các bước thủ công còn lại

## 🚀 Cài đặt và Chạy

### Bước 1: Cài đặt Node.js
Đ<PERSON>m bảo bạn đã cài đặt Node.js (phi<PERSON><PERSON> bản 14 trở lên)

### Bước 2: Cài đặt dependencies
```bash
npm install
```

### Bước 3: <PERSON><PERSON><PERSON> đặt Playwright browser
```bash
npm run install-playwright
```

### Bước 4: Chạy automation
```bash
npm start
```

## 📋 Quy trình thực hiện

### Phần tự động (Script sẽ thực hiện)
1. **Tạo Google Sheet mới** với tên "CP SEEDS Logistics App Data"
2. **Tạo 6 tabs** theo cấu trúc:
   - `Har_Plan_Input` - Lịch thu hoạch đầu vào
   - `Planning_Schedule` - Kế hoạch vận chuyển
   - `Master_Zones` - Danh mục vùng
   - `Master_Suppliers` - Danh mục nhà cung cấp
   - `Master_Vehicles` - Danh mục phương tiện
   - `Master_GroupLine` - Danh mục group line
3. **Thêm headers** cho mỗi tab
4. **Thêm dữ liệu mẫu** cho các bảng master
5. **Chia sẻ Google Sheet** với quyền truy cập phù hợp
6. **Khởi tạo AppSheet app** từ Google Sheet

### Phần thủ công (Cần thực hiện sau khi chạy script)
1. **Cấu hình bảng dữ liệu** trong AppSheet
2. **Thiết kế UX** - tạo views cho từng nhóm người dùng
3. **Triển khai Actions** - các nút hành động
4. **Thiết lập Automations** - gửi email tự động
5. **Cấu hình bảo mật** - phân quyền người dùng
6. **Triển khai và kiểm thử**

## ⚠️ Lưu ý quan trọng

### Trước khi chạy script:
- Đảm bảo đã đăng nhập vào tài khoản Google
- Có quyền tạo Google Sheets và AppSheet apps
- Đọc kỹ tài liệu hướng dẫn để hiểu quy trình

### Trong quá trình chạy:
- Script sẽ mở trình duyệt và thực hiện các thao tác tự động
- Một số bước cần can thiệp thủ công (đăng nhập, chọn file)
- Không đóng trình duyệt khi script đang chạy

### Sau khi chạy script:
- Trình duyệt sẽ được giữ mở để tiếp tục các bước thủ công
- Làm theo hướng dẫn hiển thị trong console
- Tham khảo tài liệu HTML để biết chi tiết cấu hình

## 📁 Cấu trúc dữ liệu

### Har_Plan_Input (Lịch thu hoạch)
- ID_Har_Plan, Ngày thu hoạch, Tỉnh, Huyện, Địa điểm, Loại cây trồng, Số lượng dự kiến (Kg), Ghi chú Khuyến nông

### Planning_Schedule (Kế hoạch vận chuyển)
- ID_Planning_Schedule, ID_Har_Plan_Ref, Ngày vận chuyển, Zone, Đội xe/Nhà cung cấp được giao, Số lượng dự kiến (Kg), Biển số xe, Tên tài xế, Số lượng thực tế thu hoạch (Kg), Ghi chú Đội xe, Trạng thái, Ghi chú Kế hoạch

### Master Tables
- **Master_Zones**: Zone_ID, Tên Zone, Mô tả
- **Master_Suppliers**: Supplier_ID, Tên Nhà cung cấp/Đội xe, Email_Contact, Số điện thoại
- **Master_Vehicles**: Vehicle_ID, Biển số xe, Loại xe, Thuộc_Supplier_ID
- **Master_GroupLine**: GroupLine_ID, Tên Group Line, Email_Contact

## 🔧 Troubleshooting

### Lỗi thường gặp:
1. **Không tìm thấy element**: Đợi trang load hoàn toàn
2. **Lỗi đăng nhập**: Đảm bảo đã đăng nhập Google trước
3. **Timeout**: Tăng thời gian chờ trong script

### Giải pháp:
- Chạy lại script nếu gặp lỗi
- Kiểm tra kết nối internet
- Đảm bảo trình duyệt được cập nhật

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra console log để xem lỗi chi tiết
2. Tham khảo tài liệu hướng dẫn HTML
3. Thực hiện các bước thủ công nếu automation gặp lỗi

## 📄 License

MIT License - Sử dụng tự do cho mục đích nội bộ CP SEEDS Vietnam.
