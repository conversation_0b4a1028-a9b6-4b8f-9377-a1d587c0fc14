<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tài li<PERSON>u thiết kế ứng dụng AppSheet cho CP SEEDS VIETNAM Co.LTD</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* Light gray background */
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #e0e0e0;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="p-4 md:p-8">
    <!-- Main Container -->
    <div class="max-w-4xl mx-auto bg-white shadow-xl rounded-lg p-6 md:p-10 border border-gray-200">
        <!-- Document Title -->
        <h1 class="text-3xl md:text-4xl font-bold text-indigo-800 mb-6 text-center">
            TÀI LIỆU THIẾT KẾ ỨNG DỤNG APPSHEET CHO CP SEEDS VIETNAM Co.LTD
        </h1>
        <p class="text-gray-600 text-center mb-8 text-lg">
            Giải pháp quản lý lịch thu hoạch và vận chuyển
        </p>

        <!-- Section: Introduction and Purpose -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">1. Mục đích</h2>
            <p class="text-gray-700 leading-relaxed">
                Tài liệu này nhằm mục đích ghi lại các yêu cầu nghiệp vụ chi tiết và hướng dẫn thiết kế một ứng dụng AppSheet
                hỗ trợ quy trình lập kế hoạch và quản lý vận chuyển nội bộ của C.P SEEDS VIETNAM Co.LTD.
                Ứng dụng sẽ giúp tự động hóa việc tiếp nhận lịch thu hoạch, phân chia Zone, quản lý thông tin xe và phê duyệt vận chuyển,
                nhằm nâng cao hiệu quả, giảm thiểu sai sót và cải thiện khả năng theo dõi.
            </p>
        </div>

        <!-- Section: Scope -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">2. Phạm vi</h2>
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">2.1. Phạm vi bao gồm:</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li>Tiếp nhận và xử lý lịch thu hoạch từ Bộ phận Khuyến nông.</li>
                    <li>Chức năng phân chia lịch thu hoạch theo Zone cho Bộ phận Kế hoạch.</li>
                    <li>Cơ chế gửi thông báo/lịch trình đến Đội xe/Nhà cung cấp.</li>
                    <li>Chức năng cho Đội xe/Nhà cung cấp cập nhật thông tin biển số xe và số lượng thực tế.</li>
                    <li>Chức năng duyệt và phê duyệt thông tin vận chuyển của Bộ phận Kế hoạch.</li>
                    <li>Chức năng gửi lịch vận chuyển cuối cùng đến Group Line.</li>
                    <li>Quản lý danh mục các thông tin master data liên quan (ví dụ: Tỉnh/Huyện/Địa điểm, Loại cây trồng, Danh sách Đội xe/Nhà cung cấp, Danh sách Group Line, v.v.).</li>
                </ul>
            </div>
            <div>
                <h3 class="text-xl font-medium text-gray-800 mb-3">2.2. Phạm vi không bao gồm:</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li>Quản lý kho bãi, nhập xuất kho chi tiết tại nhà máy.</li>
                    <li>Theo dõi vị trí xe thời gian thực (real-time GPS tracking).</li>
                    <li>Tính toán chi phí vận chuyển hoặc thanh toán cho Nhà cung cấp.</li>
                    <li>Tích hợp sâu với các hệ thống ERP/kế toán phức tạp khác của công ty (có thể là pha sau).</li>
                    <li>Chức năng quản lý nhân sự đội xe chi tiết (chỉ quan tâm đến thông tin xe và biển số).</li>
                </ul>
            </div>
        </div>

        <!-- Section: Stakeholders -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">3. Các bên liên quan</h2>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                <li><strong class="text-gray-800">Chủ sở hữu dự án:</strong> Ban lãnh đạo C.P SEEDS VIETNAM Co.LTD.</li>
                <li><strong class="text-gray-800">Người dùng chính:</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li>Bộ phận Kế hoạch.</li>
                        <li>Đội xe/Nhà cung cấp.</li>
                        <li>Group Line.</li>
                    </ul>
                </li>
                <li><strong class="text-gray-800">Người dùng đầu vào:</strong> Bộ phận Khuyến nông.</li>
                <li><strong class="text-gray-800">Người dùng tiếp nhận thông tin:</strong> Bộ phận Nhà máy.</li>
                <li><strong class="text-gray-800">Bộ phận IT/Phát triển:</strong> Để hỗ trợ kỹ thuật và triển khai.</li>
            </ul>
        </div>

        <!-- Section: Current Business Process -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">4. Tổng quan Quy trình Nghiệp vụ Hiện tại</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Hiện tại, quy trình đang được thực hiện thủ công hoặc bán tự động qua các bước chính sau:
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-2 mb-4">
                <li><strong class="text-gray-800">Bộ phận Khuyến nông</strong> gửi lịch thu hoạch chi tiết (file Excel/CSV).</li>
                <li><strong class="text-gray-800">Bộ phận Kế hoạch</strong> tiếp nhận, sau đó chia Zone và gửi thông tin đến Nhà cung cấp/Đội xe.</li>
                <li><strong class="text-gray-800">Nhà cung cấp/Đội xe</strong> nhận thông tin, phân xe và thực hiện thu hoạch. Sau đó cập nhật biển số xe.</li>
                <li><strong class="text-gray-800">Bộ phận Kế hoạch</strong> nhận thông tin biển số xe, sau đó gửi thông tin đến Group Line.</li>
                <li><strong class="text-gray-800">Bộ phận Nhà máy</strong> nhận thông tin và theo dõi hàng hóa.</li>
            </ol>
            <h3 class="text-xl font-medium text-gray-800 mb-3">Các vấn đề hiện tại:</h3>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                <li>Phụ thuộc vào việc gửi/nhận file thủ công (Excel), dễ gây sai sót và phiên bản lỗi thời.</li>
                <li>Thiếu khả năng theo dõi trạng thái tức thì.</li>
                <li>Khó khăn trong việc cập nhật thông tin từ Đội xe/Nhà cung cấp một cách linh hoạt.</li>
                <li>Quy trình phê duyệt có thể chậm trễ do thông tin chưa tập trung.</li>
            </ul>
        </div>

        <!-- Section: Target Business Process -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">5. Quy trình Nghiệp vụ Mục tiêu (Sau khi triển khai Ứng dụng)</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Với ứng dụng mới, quy trình sẽ được chuẩn hóa và tự động hóa hơn:
            </p>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-4">
                <li>
                    <strong class="text-gray-800">Bộ phận Khuyến nông (qua Google Sheet hoặc nhập liệu AppSheet):</strong>
                    <ul class="list-circle list-inside ml-5 mt-2">
                        <li>Bộ phận Khuyến nông sẽ nhập trực tiếp hoặc sao chép/dán dữ liệu lịch thu hoạch vào tab `Har_Plan_Input` trên Google Sheet.</li>
                        <li>AppSheet sẽ tự động đồng bộ và hiển thị dữ liệu này cho Bộ phận Kế hoạch.</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800">Bộ phận Kế hoạch (qua AppSheet):</strong>
                    <ul class="list-circle list-inside ml-5 mt-2">
                        <li>Xem danh sách các lịch thu hoạch mới (`New`).</li>
                        <li>Với mỗi lịch, Bộ phận Kế hoạch sẽ tạo một mục vận chuyển mới (hoặc chỉnh sửa mục đã có) trong tab `Planning_Schedule`.</li>
                        <li>Tại đây, Bộ phận Kế hoạch sẽ phân công:
                            <ul class="list-square list-inside ml-5">
                                <li>`ID_Planning_Schedule` (AppSheet tự tạo)</li>
                                <li>`ID_Har_Plan_Ref` (Tham chiếu đến `Har_Plan_Input`)</li>
                                <li>`Ngày vận chuyển`</li>
                                <li>`Zone` (chọn từ danh sách/tạo mới nếu cần)</li>
                                <li>`Đội xe/Nhà cung cấp được giao` (chọn từ danh sách)</li>
                                <li>`Số lượng dự kiến (Kg)` (từ input hoặc chỉnh sửa)</li>
                                <li>`Trạng thái` ban đầu là `Đã phân công`.</li>
                            </ul>
                        </li>
                        <li>Hệ thống sẽ gửi thông báo đến Đội xe/Nhà cung cấp tương ứng về lịch trình được giao.</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800">Đội xe/Nhà cung cấp (qua AppSheet - giao diện riêng):</strong>
                    <ul class="list-circle list-inside ml-5 mt-2">
                        <li>Nhận thông báo về lịch trình mới được giao.</li>
                        <li>Truy cập ứng dụng để xem các lịch trình được giao cho mình (lọc theo `Đội xe/Nhà cung cấp được giao`).</li>
                        <li>Với mỗi lịch trình, Đội xe/Nhà cung cấp có thể cập nhật các thông tin sau:
                            <ul class="list-square list-inside ml-5">
                                <li>`Biển số xe` (chọn từ danh sách xe của họ hoặc nhập mới nếu xe không có sẵn).</li>
                                <li>`Tên tài xế` (tùy chọn).</li>
                                <li>`Số lượng thực tế thu hoạch (Kg)` (tùy chọn, cập nhật sau khi thu hoạch).</li>
                                <li>`Ghi chú Đội xe` (Ví dụ: Sự cố, lý do chênh lệch số lượng).</li>
                                <li>`Trạng thái` chuyển sang `Đã cập nhật thông tin xe` (hoặc `Đã hoàn thành thu hoạch` nếu có cột `Số lượng thực tế`).</li>
                            </ul>
                        </li>
                        <li>Cập nhật `Trạng thái` của lịch trình thành `Đã cập nhật thông tin xe` sau khi hoàn tất nhập liệu.</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800">Bộ phận Kế hoạch (qua AppSheet - Duyệt):</strong>
                    <ul class="list-circle list-inside ml-5 mt-2">
                        <li>Nhận thông báo khi Đội xe/Nhà cung cấp đã cập nhật thông tin xe và số lượng.</li>
                        <li>Xem danh sách các lịch trình có trạng thái `Đã cập nhật thông tin xe`.</li>
                        <li>Duyệt và kiểm tra thông tin.</li>
                        <li>Nếu thông tin hợp lệ, Bộ phận Kế hoạch sẽ thay đổi `Trạng thái` thành `Đã duyệt` và click Action `Gửi đến Group Line`.</li>
                        <li>AppSheet sẽ tự động cập nhật thông tin này vào tab `Approved_Schedules` (hoặc gửi thông báo/email) đến Group Line và Bộ phận Nhà máy.</li>
                        <li>Nếu có vấn đề, Bộ phận Kế hoạch có thể thay đổi `Trạng thái` thành `Cần sửa đổi` và thêm `Ghi chú Kế hoạch` để Đội xe/Nhà cung cấp biết và chỉnh sửa lại.</li>
                    </ul>
                </li>
                <li>
                    <strong class="text-gray-800">Group Line & Bộ phận Nhà máy (qua AppSheet - Chế độ chỉ xem):</strong>
                    <ul class="list-circle list-inside ml-5 mt-2">
                        <li>Truy cập ứng dụng để xem các lịch trình đã được `Đã duyệt` (lọc theo trạng thái `Đã duyệt`).</li>
                        <li>Thông tin này giúp họ chuẩn bị phương án tiếp nhận và theo dõi hàng hóa.</li>
                    </ul>
                </li>
            </ol>
        </div>

        <!-- Section: Functional Requirements -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">6. Yêu cầu Chức năng (Functional Requirements - FR)</h2>
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">6.1. FR cho Bộ phận Khuyến nông (Input)</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li><strong class="text-gray-800">FR-KN-001:</strong> Có khả năng nhập/sao chép dữ liệu lịch thu hoạch vào Google Sheet (`Har_Plan_Input`).</li>
                    <li><strong class="text-gray-800">FR-KN-002:</strong> Dữ liệu có thể được chỉnh sửa nếu có sự thay đổi về lịch thu hoạch.</li>
                </ul>
            </div>
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">6.2. FR cho Bộ phận Kế hoạch</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li><strong class="text-gray-800">FR-KH-001:</strong> Xem danh sách lịch thu hoạch từ `Har_Plan_Input` (chế độ chỉ đọc) và tạo các mục vận chuyển mới dựa trên đó.</li>
                    <li><strong class="text-gray-800">FR-KH-002:</strong> Tạo/chỉnh sửa các mục trong `Planning_Schedule`, bao gồm các trường: Ngày vận chuyển, Zone, Đội xe/Nhà cung cấp, Số lượng dự kiến.</li>
                    <li><strong class="text-gray-800">FR-KH-003:</strong> Có khả năng lựa chọn `Zone` từ danh sách được định nghĩa sẵn.</li>
                    <li><strong class="text-gray-800">FR-KH-004:</strong> Có khả năng lựa chọn `Đội xe/Nhà cung cấp` từ danh sách được định nghĩa sẵn.</li>
                    <li><strong class="text-gray-800">FR-KH-005:</strong> Xem danh sách các lịch trình vận chuyển theo trạng thái (`New`, `Đã phân công`, `Đã cập nhật thông tin xe`, `Cần sửa đổi`, `Đã duyệt`).</li>
                    <li><strong class="text-gray-800">FR-KH-006:</strong> Thực hiện hành động duyệt/phê duyệt (`Approve`) một lịch trình sau khi thông tin xe được cập nhật.</li>
                    <li><strong class="text-gray-800">FR-KH-007:</strong> Thực hiện hành động `Gửi đến Group Line` sau khi duyệt.</li>
                    <li><strong class="text-gray-800">FR-KH-008:</strong> Thay đổi trạng thái thành `Cần sửa đổi` và thêm ghi chú nếu thông tin từ Đội xe/Nhà cung cấp cần chỉnh sửa.</li>
                    <li><strong class="text-gray-800">FR-KH-009:</strong> Xem chi tiết từng lịch trình vận chuyển, bao gồm thông tin cập nhật từ Đội xe/Nhà cung cấp.</li>
                    <li><strong class="text-gray-800">FR-KH-010:</strong> Lọc và tìm kiếm lịch trình theo các tiêu chí: Ngày, Zone, Đội xe/Nhà cung cấp, Trạng thái.</li>
                </ul>
            </div>
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">6.3. FR cho Đội xe/Nhà cung cấp</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li><strong class="text-gray-800">FR-TX-001:</strong> Đăng nhập vào ứng dụng và chỉ xem được các lịch trình vận chuyển được phân công cho mình.</li>
                    <li><strong class="text-gray-800">FR-TX-002:</strong> Chỉnh sửa thông tin lịch trình được giao, đặc biệt là cập nhật `Biển số xe`, `Tên tài xế`, `Số lượng thực tế thu hoạch` và `Ghi chú Đội xe`.</li>
                    <li><strong class="text-gray-800">FR-TX-003:</strong> Có khả năng lựa chọn `Biển số xe` từ danh sách xe của họ (nếu có master data xe riêng) hoặc nhập tự do.</li>
                    <li><strong class="text-gray-800">FR-TX-004:</strong> Cập nhật `Trạng thái` của lịch trình thành `Đã cập nhật thông tin xe` sau khi hoàn tất nhập liệu.</li>
                    <li><strong class="text-gray-800">FR-TX-005:</strong> Nhận thông báo khi có lịch trình mới được phân công hoặc khi Bộ phận Kế hoạch yêu cầu sửa đổi.</li>
                </ul>
            </div>
            <div>
                <h3 class="text-xl font-medium text-gray-800 mb-3">6.4. FR cho Group Line & Bộ phận Nhà máy</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li><strong class="text-gray-800">FR-GL-001:</strong> Đăng nhập vào ứng dụng và chỉ xem được các lịch trình vận chuyển đã được `Đã duyệt`.</li>
                    <li><strong class="text-gray-800">FR-GL-002:</strong> Xem chi tiết các thông tin về ngày vận chuyển, địa điểm, số lượng, biển số xe và tài xế.</li>
                    <li><strong class="text-gray-800">FR-GL-003:</strong> Lọc và tìm kiếm lịch trình theo các tiêu chí: Ngày, Zone, Đội xe/Nhà cung cấp.</li>
                </ul>
            </div>
        </div>

        <!-- Section: Non-Functional Requirements -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">7. Yêu cầu Phi chức năng (Non-Functional Requirements - NFR)</h2>
            <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                <li><strong class="text-gray-800">NFR-001 - Hiệu suất:</strong> Ứng dụng phải tải nhanh và thao tác mượt mà ngay cả khi có nhiều dữ liệu (ước tính 1000+ dòng/năm).</li>
                <li><strong class="text-gray-800">NFR-002 - Bảo mật:</strong>
                    <ul class="list-circle list-inside ml-5">
                        <li>Phân quyền rõ ràng cho từng vai trò (Bộ phận Kế hoạch, Đội xe/Nhà cung cấp, Group Line/Nhà máy).</li>
                        <li>Dữ liệu được bảo vệ khỏi truy cập trái phép.</li>
                        <li>Đảm bảo dữ liệu trên Google Sheet được sao lưu định kỳ.</li>
                    </ul>
                </li>
                <li><strong class="text-gray-800">NFR-003 - Khả năng sử dụng (Usability):</strong> Giao diện thân thiện, dễ hiểu, dễ sử dụng cho tất cả các đối tượng người dùng, đặc biệt là Đội xe/Nhà cung cấp có thể sử dụng trên điện thoại di động. Các thông báo và hướng dẫn rõ ràng.</li>
                <li><strong class="text-gray-800">NFR-004 - Khả năng mở rộng (Scalability):</strong> Có khả năng mở rộng để đáp ứng nhu cầu tăng trưởng về số lượng lịch trình và người dùng trong tương lai.</li>
                <li><strong class="text-gray-800">NFR-005 - Khả năng tích hợp:</strong> Có khả năng tích hợp với hệ thống email (để gửi thông báo).</li>
                <li><strong class="text-gray-800">NFR-006 - Khả năng truy cập:</strong> Ứng dụng phải hoạt động tốt trên các thiết bị di động (smartphone, tablet) và máy tính để bàn (web browser).</li>
            </ul>
        </div>

        <!-- Section: Implementation Options Analysis -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">8. Phân tích lựa chọn triển khai</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Chúng ta sẽ xem xét 2 phương án chính: <strong class="text-gray-800">AppSheet</strong> và <strong class="text-gray-800">Zalo Mini App</strong>.
            </p>

            <!-- AppSheet Option -->
            <div class="mb-8">
                <h3 class="text-xl font-medium text-gray-800 mb-3">8.1. Phương án 1: AppSheet</h3>
                <div class="mb-4">
                    <h4 class="text-lg font-medium text-gray-700 mb-2">Ưu điểm:</h4>
                    <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                        <li><strong class="text-gray-800">Tích hợp sâu với Google Sheet:</strong> Dữ liệu được lưu trữ trực tiếp trên Google Sheet, dễ dàng quản lý và chỉnh sửa thủ công nếu cần.</li>
                        <li><strong class="text-gray-800">Phát triển nhanh (Low-code/No-code):</strong> Thời gian phát triển nhanh, không yêu cầu kiến thức lập trình chuyên sâu.</li>
                        <li><strong class="text-gray-800">Tính năng mạnh mẽ:</strong> AppSheet cung cấp nhiều tính năng có sẵn như form nhập liệu, bảng biểu, lọc, tìm kiếm, workflow tự động (Automation), gửi email/thông báo.</li>
                        <li><strong class="text-gray-800">Phân quyền linh hoạt:</strong> Dễ dàng cấu hình phân quyền xem/chỉnh sửa dữ liệu dựa trên vai trò người dùng (email).</li>
                        <li><strong class="text-gray-800">Hỗ trợ đa nền tảng:</strong> Ứng dụng chạy tốt trên cả trình duyệt web và ứng dụng di động (iOS/Android).</li>
                        <li><strong class="text-gray-800">Tích hợp với Google Workspace:</strong> Dễ dàng kết nối với Gmail, Google Drive, Google Calendar, v.v.</li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-medium text-gray-700 mb-2">Nhược điểm:</h4>
                    <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                        <li><strong class="text-gray-800">Chi phí:</strong> Yêu cầu bản quyền AppSheet (thường là gói Core hoặc Enterprise) tùy theo số lượng người dùng và tính năng sử dụng.</li>
                        <li><strong class="text-gray-800">Tùy biến giao diện hạn chế:</strong> Giao diện được xây dựng theo khuôn mẫu của AppSheet, ít linh hoạt trong việc tùy biến hoàn toàn theo ý muốn (ví dụ: không thể tạo giao diện độc đáo như ứng dụng native).</li>
                        <li><strong class="text-gray-800">Phụ thuộc vào hệ sinh thái Google:</strong> Cần có tài khoản Google để sử dụng và quản lý.</li>
                        <li><strong class="text-gray-800">Trải nghiệm người dùng (UX) cho người dùng không quen:</strong> Đôi khi, người dùng không quen với AppSheet cần một thời gian để làm quen.</li>
                    </ul>
                </div>
                <h4 class="text-lg font-medium text-gray-700 mt-4 mb-2">Khuyến nghị cho AppSheet:</h4>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li>Sử dụng email của người dùng (nội bộ công ty và nhà cung cấp có thể đăng ký tài khoản Google) để phân quyền.</li>
                    <li>Xây dựng các View riêng cho từng vai trò (Bộ phận Kế hoạch, Đội xe/Nhà cung cấp, Group Line/Nhà máy) để hiển thị thông tin phù hợp.</li>
                    <li>Sử dụng Automation để gửi email thông báo khi trạng thái thay đổi hoặc có lịch trình mới được giao.</li>
                </ul>
            </div>

            <!-- Zalo Mini App Option -->
            <div class="mb-8">
                <h3 class="text-xl font-medium text-gray-800 mb-3">8.2. Phương án 2: Zalo Mini App</h3>
                <div class="mb-4">
                    <h4 class="text-lg font-medium text-gray-700 mb-2">Ưu điểm:</h4>
                    <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                        <li><strong class="text-gray-800">Tiếp cận người dùng Zalo:</strong> Rất phù hợp nếu phần lớn Đội xe/Nhà cung cấp và các bên liên quan khác sử dụng Zalo thường xuyên, giảm rào cản cài đặt ứng dụng mới.</li>
                        <li><strong class="text-gray-800">Giao diện tùy biến cao:</strong> Zalo Mini App cho phép tùy biến giao diện gần như hoàn toàn, tạo trải nghiệm người dùng phù hợp với thương hiệu.</li>
                        <li><strong class="text-gray-800">Thông báo đẩy (Push Notification):</strong> Dễ dàng gửi thông báo đẩy trực tiếp qua Zalo đến người dùng.</li>
                        <li><strong class="text-gray-800">Không cần cài đặt ứng dụng riêng:</strong> Người dùng chỉ cần mở Zalo và tìm Mini App.</li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-medium text-gray-700 mb-2">Nhược điểm:</h4>
                    <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                        <li><strong class="text-gray-800">Yêu cầu lập trình:</strong> Phát triển Zalo Mini App yêu cầu kiến thức lập trình (HTML, CSS, JavaScript và API của Zalo), tốn thời gian và chi phí hơn AppSheet.</li>
                        <li><strong class="text-gray-800">Quản lý dữ liệu:</strong> Cần một hệ thống backend để quản lý dữ liệu (ví dụ: một máy chủ với cơ sở dữ liệu như PostgreSQL, MySQL, hoặc Google Cloud Firestore, Firebase Realtime Database) và các API để Zalo Mini App tương tác.</li>
                        <li><strong class="text-gray-800">Phức tạp trong phân quyền:</strong> Việc quản lý phân quyền và xác thực người dùng có thể phức tạp hơn so với AppSheet (phải tự xây dựng).</li>
                        <li><strong class="text-gray-800">Phụ thuộc vào Zalo:</strong> Quy định và chính sách của Zalo có thể ảnh hưởng đến Mini App.</li>
                        <li><strong class="text-gray-800">Triển khai và bảo trì:</strong> Cần đội ngũ kỹ thuật để triển khai, vận hành và bảo trì.</li>
                    </ul>
                </div>
                <h4 class="text-lg font-medium text-gray-700 mt-4 mb-2">Khuyến nghị cho Zalo Mini App:</h4>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li>Phù hợp nếu C.P SEEDS có đội ngũ lập trình hoặc ngân sách cho việc thuê ngoài để phát triển và bảo trì.</li>
                    <li>Dữ liệu có thể được lưu trữ trên một nền tảng cơ sở dữ liệu cloud (như Google Firestore, Firebase) và có API để Zalo Mini App đọc/ghi dữ liệu.</li>
                    <li>Sử dụng API của Zalo để gửi thông báo.</li>
                </ul>
            </div>

            <!-- Comparison Table -->
            <h3 class="text-xl font-medium text-gray-800 mb-3">8.3. So sánh & Đề xuất</h3>
            <div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
                <table class="min-w-full table-auto text-left whitespace-nowrap">
                    <thead class="bg-indigo-600 text-white">
                        <tr>
                            <th class="px-4 py-3 text-lg font-semibold rounded-tl-lg">Tiêu chí</th>
                            <th class="px-4 py-3 text-lg font-semibold">AppSheet</th>
                            <th class="px-4 py-3 text-lg font-semibold rounded-tr-lg">Zalo Mini App</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Khả năng phát triển</td>
                            <td class="px-4 py-3 text-gray-700">Nhanh (Low-code/No-code)</td>
                            <td class="px-4 py-3 text-gray-700">Yêu cầu lập trình</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Chi phí ban đầu</td>
                            <td class="px-4 py-3 text-gray-700">Thấp hơn</td>
                            <td class="px-4 py-3 text-gray-700">Cao hơn</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Khả năng tích hợp dữ liệu</td>
                            <td class="px-4 py-3 text-gray-700">Google Sheet, Cloud SQL, Salesforce, v.v. (tích hợp sẵn)</td>
                            <td class="px-4 py-3 text-gray-700">Yêu cầu API backend riêng</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Giao diện tùy biến</td>
                            <td class="px-4 py-3 text-gray-700">Hạn chế (theo khuôn mẫu)</td>
                            <td class="px-4 py-3 text-gray-700">Cao (tùy biến hoàn toàn)</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Phân quyền người dùng</td>
                            <td class="px-4 py-3 text-gray-700">Linh hoạt, dễ cấu hình (dựa trên email)</td>
                            <td class="px-4 py-3 text-gray-700">Cần xây dựng riêng</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Thông báo</td>
                            <td class="px-4 py-3 text-gray-700">Email, SMS (qua tích hợp)</td>
                            <td class="px-4 py-3 text-gray-700">Push Notification qua Zalo</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Tiếp cận người dùng</td>
                            <td class="px-4 py-3 text-gray-700">Yêu cầu cài đặt AppSheet hoặc truy cập web</td>
                            <td class="px-4 py-3 text-gray-700">Nằm trong Zalo (tiện lợi nếu người dùng quen Zalo)</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Bảo trì/Hỗ trợ</td>
                            <td class="px-4 py-3 text-gray-700">Dễ hơn (ít code)</td>
                            <td class="px-4 py-3 text-gray-700">Phức tạp hơn (cần đội ngũ kỹ thuật)</td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3 font-medium text-gray-800">Độ phức tạp dự án</td>
                            <td class="px-4 py-3 text-gray-700">Thấp</td>
                            <td class="px-4 py-3 text-gray-700">Trung bình - Cao</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <p class="text-gray-700 leading-relaxed mt-4">
                <strong class="text-indigo-800">Đề xuất:</strong> Đối với nhu cầu hiện tại của C.P SEEDS VIETNAM Co.LTD, đặc biệt là ưu tiên về tốc độ triển khai, chi phí ban đầu thấp và khả năng tích hợp sẵn với Google Workspace, <strong class="text-indigo-800">AppSheet là lựa chọn phù hợp và hiệu quả hơn.</strong> Zalo Mini App sẽ là một lựa chọn tiềm năng cho giai đoạn sau, khi có nhu cầu về giao diện tùy biến sâu hơn và mở rộng sang các tính năng tích hợp Zalo khác.
            </p>
        </div>

        <!-- Section: Google Sheet Structure Details -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">9. Cấu trúc Google Sheet Chi tiết</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Đây là các tab chính bạn cần thiết lập trong Google Sheet của mình:
            </p>

            <!-- Har_Plan_Input Table -->
            <div class="mb-8">
                <h3 class="text-xl font-medium text-gray-800 mb-3">9.1. Tab: `Har_Plan_Input` (Đầu vào từ Bộ phận Khuyến nông)</h3>
                <p class="text-gray-700 leading-relaxed mb-3">
                    Tab này dùng để lưu trữ dữ liệu lịch thu hoạch thô ban đầu.
                </p>
                <div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
                    <table class="min-w-full table-auto text-left">
                        <thead class="bg-blue-600 text-white">
                            <tr>
                                <th class="px-4 py-3 font-semibold rounded-tl-lg">Tên Cột</th>
                                <th class="px-4 py-3 font-semibold">Loại Dữ liệu</th>
                                <th class="px-4 py-3 font-semibold rounded-tr-lg">Ghi chú</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`ID_Har_Plan`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700">Khóa chính. AppSheet có thể tự tạo bằng `UNIQUEID()`</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Ngày thu hoạch`</td>
                                <td class="px-4 py-3 text-gray-700">Ngày (Date)</td>
                                <td class="px-4 py-3 text-gray-700"></td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Tỉnh`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700"></td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Huyện`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700"></td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Địa điểm`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700"></td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Loại cây trồng`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700"></td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Số lượng dự kiến (Kg)`</td>
                                <td class="px-4 py-3 text-gray-700">Số (Number)</td>
                                <td class="px-4 py-3 text-gray-700"></td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Ghi chú Khuyến nông`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700">Tùy chọn</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Planning_Schedule Table -->
            <div class="mb-8">
                <h3 class="text-xl font-medium text-gray-800 mb-3">9.2. Tab: `Planning_Schedule` (Dữ liệu làm việc của Bộ phận Kế hoạch)</h3>
                <p class="text-gray-700 leading-relaxed mb-3">
                    Đây là tab trung tâm, nơi Bộ phận Kế hoạch sẽ làm việc.
                </p>
                <div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
                    <table class="min-w-full table-auto text-left">
                        <thead class="bg-blue-600 text-white">
                            <tr>
                                <th class="px-4 py-3 font-semibold rounded-tl-lg">Tên Cột</th>
                                <th class="px-4 py-3 font-semibold">Loại Dữ liệu</th>
                                <th class="px-4 py-3 font-semibold rounded-tr-lg">Ghi chú</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`ID_Planning_Schedule`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700">Khóa chính. AppSheet tự tạo.</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`ID_Har_Plan_Ref`</td>
                                <td class="px-4 py-3 text-gray-700">Ref (Tham chiếu)</td>
                                <td class="px-4 py-3 text-gray-700">Tham chiếu đến `Har_Plan_Input`</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Ngày vận chuyển`</td>
                                <td class="px-4 py-3 text-gray-700">Ngày (Date)</td>
                                <td class="px-4 py-3 text-gray-700">Ngày do BP Kế hoạch lên kế hoạch.</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Zone`</td>
                                <td class="px-4 py-3 text-gray-700">Enum / Văn bản</td>
                                <td class="px-4 py-3 text-gray-700">Chọn từ danh sách (nên tạo tab Master Data Zone).</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Đội xe/Nhà cung cấp được giao`</td>
                                <td class="px-4 py-3 text-gray-700">Ref / Văn bản</td>
                                <td class="px-4 py-3 text-gray-700">Chọn từ danh sách (nên tạo tab Master Data Nhà cung cấp).</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Số lượng dự kiến (Kg)`</td>
                                <td class="px-4 py-3 text-gray-700">Số (Number)</td>
                                <td class="px-4 py-3 text-gray-700">Số lượng ban đầu.</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Biển số xe`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700">Đội xe cập nhật.</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Tên tài xế`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700">Đội xe cập nhật (tùy chọn).</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Số lượng thực tế thu hoạch (Kg)`</td>
                                <td class="px-4 py-3 text-gray-700">Số (Number)</td>
                                <td class="px-4 py-3 text-gray-700">Đội xe cập nhật.</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Ghi chú Đội xe`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700">Đội xe ghi chú.</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Trạng thái`</td>
                                <td class="px-4 py-3 text-gray-700">Enum</td>
                                <td class="px-4 py-3 text-gray-700">`New`, `Đã phân công`, `Đã cập nhật thông tin xe`, `Cần sửa đổi`, `Đã duyệt`, `Đã hoàn thành`.</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 font-medium text-gray-800">`Ghi chú Kế hoạch`</td>
                                <td class="px-4 py-3 text-gray-700">Văn bản (Text)</td>
                                <td class="px-4 py-3 text-gray-700">BP Kế hoạch ghi chú khi cần sửa đổi.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Master Data Tabs -->
            <div class="mb-8">
                <h3 class="text-xl font-medium text-gray-800 mb-3">9.3. Các Tab Master Data (Tham khảo)</h3>
                <p class="text-gray-700 leading-relaxed mb-3">
                    Để quản lý danh sách `Zone`, `Nhà cung cấp` và `Biển số xe`, bạn nên tạo các tab riêng:
                </p>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li>
                        <strong class="text-gray-800">Tab: `Master_Zones`</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li>`Zone_ID` (Khóa chính)</li>
                            <li>`Tên Zone`</li>
                            <li>`Mô tả`</li>
                        </ul>
                    </li>
                    <li>
                        <strong class="text-gray-800">Tab: `Master_Suppliers` (Nhà cung cấp/Đội xe)</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li>`Supplier_ID` (Khóa chính)</li>
                            <li>`Tên Nhà cung cấp/Đội xe`</li>
                            <li>`Email_Contact` (Quan trọng cho phân quyền AppSheet)</li>
                            <li>`Số điện thoại`</li>
                        </ul>
                    </li>
                    <li>
                        <strong class="text-gray-800">Tab: `Master_Vehicles` (Danh sách xe)</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li>`Vehicle_ID` (Khóa chính)</li>
                            <li>`Biển số xe`</li>
                            <li>`Loại xe`</li>
                            <li>`Thuộc_Supplier_ID` (Tham chiếu đến `Master_Suppliers`)</li>
                        </ul>
                    </li>
                    <li>
                        <strong class="text-gray-800">Tab: `Master_GroupLine` (Danh sách Group Line)</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li>`GroupLine_ID` (Khóa chính)</li>
                            <li>`Tên Group Line`</li>
                            <li>`Email_Contact` (Để gửi thông báo)</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Section: AppSheet Design Guidelines -->
        <div class="mb-10 p-6 bg-indigo-50 rounded-lg shadow-sm border border-indigo-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">10. Hướng dẫn thiết kế AppSheet</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
                Sau khi thiết lập Google Sheet, bạn có thể bắt đầu xây dựng ứng dụng AppSheet.
            </p>

            <!-- Data (Dữ liệu) -->
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">10.1. Data (Dữ liệu)</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li><strong class="text-gray-800">Thêm Bảng dữ liệu:</strong> Kết nối Google Sheet của bạn với AppSheet. Thêm tất cả các tab (`Har_Plan_Input`, `Planning_Schedule`, `Master_Zones`, `Master_Suppliers`, `Master_Vehicles`, `Master_GroupLine`) vào mục "Data" trong AppSheet.</li>
                    <li><strong class="text-gray-800">Cấu hình cột:</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li>Đảm bảo các cột `ID_Har_Plan`, `ID_Planning_Schedule`, `Zone_ID`, `Supplier_ID`, `Vehicle_ID`, `GroupLine_ID` được thiết lập làm `Key` và `Label`.</li>
                            <li>Thiết lập loại dữ liệu phù hợp (Date, Number, Text, Enum, Ref).</li>
                            <li>Đối với `ID_Har_Plan_Ref`, `Đội xe/Nhà cung cấp được giao`, `Thuộc_Supplier_ID`, hãy thiết lập loại `Ref` và chọn bảng tham chiếu tương ứng.</li>
                            <li>Cột `Trạng thái` nên là kiểu `Enum` với các giá trị: `New`, `Đã phân công`, `Đã cập nhật thông tin xe`, `Cần sửa đổi`, `Đã duyệt`, `Đã hoàn thành`.</li>
                        </ul>
                    </li>
                </ul>
            </div>

            <!-- UX (Giao diện người dùng) -->
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">10.2. UX (Giao diện người dùng)</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li><strong class="text-gray-800">Tạo Views cho từng vai trò:</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li><strong class="text-gray-800">Bộ phận Kế hoạch:</strong>
                                <ul class="list-square list-inside ml-5">
                                    <li>`Lịch Thu Hoạch Mới (Har_Plan_Input)`: View dạng Table hoặc Deck (chỉ xem) để xem lịch thu hoạch ban đầu.</li>
                                    <li>`Kế Hoạch Vận Chuyển (Planning_Schedule)`: View dạng Table hoặc Deck để quản lý và phân công.
                                        <ul>
                                            <li>Tạo Form để thêm mới/chỉnh sửa mục vận chuyển.</li>
                                            <li>Sử dụng các biểu thức `Valid_If` để giới hạn lựa chọn `Zone`, `Đội xe/Nhà cung cấp`.</li>
                                            <li>Thiết lập các Action Buttons: `Phân công`, `Duyệt`, `Gửi đến Group Line`, `Yêu cầu sửa đổi`.</li>
                                        </ul>
                                    </li>
                                    <li>`Lịch Đã Duyệt (Planning_Schedule - lọc trạng thái)`: View dạng Table/Deck chỉ hiển thị các lịch đã được duyệt.</li>
                                </ul>
                            </li>
                            <li><strong class="text-gray-800">Đội xe/Nhà cung cấp:</strong>
                                <ul class="list-square list-inside ml-5">
                                    <li>`Lịch Trình Của Tôi (Planning_Schedule)`: View dạng Table/Deck, được lọc chỉ hiển thị các mục mà `Đội xe/Nhà cung cấp được giao` là người dùng hiện tại (`USEREMAIL()` hoặc `LOOKUP()` email của họ trong `Master_Suppliers`).</li>
                                    <li>Tạo Form để cập nhật thông tin: `Biển số xe`, `Tên tài xế`, `Số lượng thực tế thu hoạch`, `Ghi chú Đội xe`.</li>
                                    <li>Action Button: `Đã cập nhật thông tin xe` (chuyển trạng thái).</li>
                                </ul>
                            </li>
                            <li><strong class="text-gray-800">Group Line & Bộ phận Nhà máy:</strong>
                                <ul class="list-square list-inside ml-5">
                                    <li>`Lịch Vận Chuyển Đã Duyệt (Planning_Schedule)`: View dạng Table/Deck, chỉ hiển thị các mục có `Trạng thái` là `Đã duyệt`. (chế độ chỉ xem)</li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                    <li><strong class="text-gray-800">Giao diện di động:</strong> Thiết kế các View và Form thân thiện với di động, sử dụng các loại View như Deck, Card, Detail để hiển thị thông tin rõ ràng.</li>
                </ul>
            </div>

            <!-- Automation (Tự động hóa) -->
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">10.3. Automation (Tự động hóa)</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li><strong class="text-gray-800">Automation: `Notify_Supplier_Assigned`</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li><strong class="text-gray-800">Event:</strong> Khi một dòng trong `Planning_Schedule` được thêm mới hoặc cập nhật và `Trạng thái` chuyển sang `Đã phân công`.</li>
                            <li><strong class="text-gray-800">Process:</strong> Gửi email thông báo cho `Email_Contact` của `Đội xe/Nhà cung cấp được giao` (sử dụng LOOKUP để lấy email từ `Master_Suppliers`).</li>
                            <li><strong class="text-gray-800">Nội dung email:</strong> Thông báo về lịch trình mới, yêu cầu cập nhật thông tin xe.</li>
                        </ul>
                    </li>
                    <li><strong class="text-gray-800">Automation: `Notify_Planning_Updated_By_Supplier`</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li><strong class="text-gray-800">Event:</strong> Khi một dòng trong `Planning_Schedule` được cập nhật và `Trạng thái` chuyển sang `Đã cập nhật thông tin xe`.</li>
                            <li><strong class="text-gray-800">Process:</strong> Gửi email thông báo cho Bộ phận Kế hoạch (có thể là một email cố định hoặc danh sách email).</li>
                            <li><strong class="text-gray-800">Nội dung email:</strong> Thông báo có lịch trình cần duyệt.</li>
                        </ul>
                    </li>
                    <li><strong class="text-gray-800">Automation: `Notify_GroupLine_Approved`</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li><strong class="text-gray-800">Event:</strong> Khi một dòng trong `Planning_Schedule` được cập nhật và `Trạng thái` chuyển sang `Đã duyệt` (và BP Kế hoạch thực hiện action `Gửi đến Group Line`).</li>
                            <li><strong class="text-gray-800">Process:</strong> Gửi email thông báo cho `Email_Contact` của Group Line (lấy từ `Master_GroupLine`) và Bộ phận Nhà máy.</li>
                            <li><strong class="text-gray-800">Nội dung email:</strong> Chi tiết lịch trình đã duyệt và sẵn sàng vận chuyển.</li>
                        </ul>
                    </li>
                    <li><strong class="text-gray-800">Automation: `Notify_Supplier_Need_Revision`</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li><strong class="text-gray-800">Event:</strong> Khi một dòng trong `Planning_Schedule` được cập nhật và `Trạng thái` chuyển sang `Cần sửa đổi`.</li>
                            <li><strong class="text-gray-800">Process:</strong> Gửi email thông báo cho `Email_Contact` của `Đội xe/Nhà cung cấp được giao`.</li>
                            <li><strong class="text-gray-800">Nội dung email:</strong> Thông báo về yêu cầu sửa đổi, kèm theo `Ghi chú Kế hoạch`.</li>
                        </ul>
                    </li>
                </ul>
            </div>

            <!-- Security (Bảo mật) -->
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">10.4. Security (Bảo mật)</h3>
                <ul class="list-disc list-inside text-gray-700 leading-relaxed space-y-2">
                    <li><strong class="text-gray-800">Authentication:</strong> Thiết lập AppSheet sử dụng Google Authentication.</li>
                    <li><strong class="text-gray-800">User Roles (Phân quyền người dùng):</strong>
                        <ul class="list-circle list-inside ml-5">
                            <li>Tạo các vai trò trong AppSheet: `KeHoach`, `DoiXe_NhaCungCap`, `GroupLine_NhaMay`.</li>
                            <li>Sử dụng biểu thức `USEREMAIL()` và các bảng Master Data để gán vai trò:
                                <ul class="list-square list-inside ml-5">
                                    <li>Người dùng `KeHoach` có thể chỉnh sửa `Planning_Schedule` và các bảng Master Data.</li>
                                    <li>Người dùng `DoiXe_NhaCungCap` chỉ có thể xem/chỉnh sửa các dòng trong `Planning_Schedule` mà họ được phân công (sử dụng Security Filter trên bảng `Planning_Schedule`: `[Đội xe/Nhà cung cấp được giao].[Email_Contact] = USEREMAIL()`). Họ không thể thêm mới hoặc xóa các dòng này.</li>
                                    <li>Người dùng `GroupLine_NhaMay` chỉ có thể xem các dòng trong `Planning_Schedule` có `Trạng thái` là `Đã duyệt` (sử dụng Security Filter: `[Trạng thái] = "Đã duyệt"`).</li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Section: Next Steps -->
        <div class="mb-10 p-6 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-2xl font-semibold text-indigo-700 mb-4">11. Các bước tiếp theo</h2>
            <ol class="list-decimal list-inside text-gray-700 leading-relaxed space-y-2">
                <li>Tạo Google Sheet với cấu trúc các tab và cột như đã mô tả.</li>
                <li>Nhập một ít dữ liệu mẫu vào các tab Master Data để kiểm thử.</li>
                <li>Tạo ứng dụng AppSheet mới và kết nối với Google Sheet này.</li>
                <li>Thiết lập các bảng dữ liệu, loại cột, khóa chính và tham chiếu (Ref).</li>
                <li>Xây dựng các View (giao diện) và Form cho từng vai trò người dùng.</li>
                <li>Cấu hình các Action Buttons và Automation để xử lý quy trình.</li>
                <li>Thiết lập phân quyền người dùng (Security Filter) cho từng vai trò.</li>
                <li>Thực hiện kiểm thử kỹ lưỡng với dữ liệu mẫu và các vai trò khác nhau.</li>
            </ol>
        </div>

        <p class="text-sm text-gray-500 text-center mt-10">
            Tài liệu này được tạo ra để hỗ trợ triển khai ứng dụng quản lý lịch thu hoạch và vận chuyển cho C.P SEEDS VIETNAM Co.LTD.
        </p>
    </div>
</body>
</html>
